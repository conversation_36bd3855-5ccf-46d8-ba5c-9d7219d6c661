<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\News;
use Carbon\Carbon;

class SearchController extends Controller
{
    public function search(Request $request)
    {
        $query = $request->input('query');
        
        if (empty($query)) {
            return response()->json([
                'html' => '',
                'count' => 0,
                'total' => 0
            ]);
        }
        
        // Get total count for all matching articles
        $totalCount = News::published()
            ->where(function($q) use ($query) {
                $q->where('title', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%")
                  ->orWhere('meta_description', 'like', "%{$query}%");
            })
            ->count();
        
        // Search for news articles that match the query (limited to 10)
        $articles = News::published()
            ->where(function($q) use ($query) {
                $q->where('title', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%")
                  ->orWhere('meta_description', 'like', "%{$query}%");
            })
            ->with('categories') // Eager load the categories relationship
            ->orderBy('published_at', 'desc')
            ->limit(10)
            ->get();
            
        // Generate HTML view
        $html = view('frontend.partials.search-results', [
            'articles' => $articles,
            'query' => $query,
            'totalCount' => $totalCount
        ])->render();
        
        return response()->json([
            'html' => $html,
            'count' => $articles->count(),
            'total' => $totalCount
        ]);
    }
    
    public function searchPage(Request $request)
    {
        $query = $request->input('query', '');
        
        if (empty($query)) {
            return redirect()->route('home');
        }
        
        // Get initial search results
        $articles = News::published()
            ->where(function($q) use ($query) {
                $q->where('title', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%")
                  ->orWhere('meta_description', 'like', "%{$query}%");
            })
            ->with('categories')
            ->orderBy('published_at', 'desc')
            ->limit(20)
            ->get();
            
        return view('frontend.search', compact('articles', 'query'));
    }
    
    public function loadMoreResults(Request $request)
    {
        $query = $request->input('query', '');
        $offset = $request->input('offset', 0);
        $limit = $request->input('limit', 10);
        
        if (empty($query)) {
            return response()->json([
                'html' => '',
                'hasMore' => false
            ]);
        }
        
        // Get more search results
        $moreArticles = News::published()
            ->where(function($q) use ($query) {
                $q->where('title', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%")
                  ->orWhere('meta_description', 'like', "%{$query}%");
            })
            ->with('categories')
            ->orderBy('published_at', 'desc')
            ->skip($offset)
            ->take($limit)
            ->get();
            
        if ($moreArticles->isEmpty()) {
            return response()->json([
                'html' => '',
                'hasMore' => false
            ]);
        }
        
        // Check if there are more results
        $nextResults = News::published()
            ->where(function($q) use ($query) {
                $q->where('title', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%")
                  ->orWhere('meta_description', 'like', "%{$query}%");
            })
            ->skip($offset + $limit)
            ->take(1)
            ->exists();
            
        $html = view('frontend.partials.search-results-items', [
            'articles' => $moreArticles
        ])->render();
        
        return response()->json([
            'html' => $html,
            'hasMore' => $nextResults
        ]);
    }
}





