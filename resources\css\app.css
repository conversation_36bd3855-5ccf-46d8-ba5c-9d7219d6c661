@import "tailwindcss";
@import "../../public/css/global.css";

@config "../../tailwind.config.js";

/* Roboto Font Family - Local Font Files */
@font-face {
    font-family: 'Roboto';
    src: url('../../public/fonts/roboto/Roboto-Thin.woff2') format('woff2');
    font-weight: 100;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Roboto';
    src: url('../../public/fonts/roboto/Roboto-ThinItalic.woff2') format('woff2');
    font-weight: 100;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Roboto';
    src: url('../../public/fonts/roboto/Roboto-Light.woff2') format('woff2');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Roboto';
    src: url('../../public/fonts/roboto/Roboto-LightItalic.woff2') format('woff2');
    font-weight: 300;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Roboto';
    src: url('../../public/fonts/roboto/Roboto-Regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Roboto';
    src: url('../../public/fonts/roboto/Roboto-Italic.woff2') format('woff2');
    font-weight: 400;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Roboto';
    src: url('../../public/fonts/roboto/Roboto-Medium.woff2') format('woff2');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Roboto';
    src: url('../../public/fonts/roboto/Roboto-MediumItalic.woff2') format('woff2');
    font-weight: 500;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Roboto';
    src: url('../../public/fonts/roboto/Roboto-Bold.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Roboto';
    src: url('../../public/fonts/roboto/Roboto-BoldItalic.woff2') format('woff2');
    font-weight: 700;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Roboto';
    src: url('../../public/fonts/roboto/Roboto-Black.woff2') format('woff2');
    font-weight: 900;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Roboto';
    src: url('../../public/fonts/roboto/Roboto-BlackItalic.woff2') format('woff2');
    font-weight: 900;
    font-style: italic;
    font-display: swap;
}

@theme {
    --font-sans: 'Roboto', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol', 'Noto Color Emoji';

    /* Material Design Color Palette */
    --color-primary: #1976d2;
    --color-primary-50: #e3f2fd;
    --color-primary-100: #bbdefb;
    --color-primary-200: #90caf9;
    --color-primary-300: #64b5f6;
    --color-primary-400: #42a5f5;
    --color-primary-500: #2196f3;
    --color-primary-600: #1e88e5;
    --color-primary-700: #1976d2;
    --color-primary-800: #1565c0;
    --color-primary-900: #0d47a1;

    --color-secondary: #dc004e;
    --color-secondary-50: #fce4ec;
    --color-secondary-100: #f8bbd9;
    --color-secondary-200: #f48fb1;
    --color-secondary-300: #f06292;
    --color-secondary-400: #ec407a;
    --color-secondary-500: #e91e63;
    --color-secondary-600: #d81b60;
    --color-secondary-700: #c2185b;
    --color-secondary-800: #ad1457;
    --color-secondary-900: #880e4f;

    --color-success: #4caf50;
    --color-warning: #ff9800;
    --color-error: #f44336;
    --color-info: #2196f3;

    /* Material Design Shadows */
    --shadow-sm: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    --shadow-md: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
    --shadow-lg: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
    --shadow-xl: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);
}

/* Dark Theme Support */
.dark {
    /* Dark theme color overrides */
    --color-primary: #ef4444;
    --color-primary-hover: #dc2626;
    --color-primary-light: #1f2937;

    --color-secondary: #9ca3af;
    --color-secondary-hover: #d1d5db;
    --color-secondary-light: #1f2937;

    --color-accent: #60a5fa;
    --color-accent-hover: #3b82f6;
    --color-accent-light: #1e293b;

    --color-text-primary: #f9fafb;
    --color-text-secondary: #d1d5db;
    --color-text-muted: #9ca3af;

    --color-border: #374151;
    --color-border-light: #4b5563;

    --color-background: #111827;
    --color-background-secondary: #1f2937;
    --color-background-muted: #374151;

    /* Dark theme shadows */
    --shadow-sm: 0 1px 3px rgba(0,0,0,0.3), 0 1px 2px rgba(0,0,0,0.4);
    --shadow-md: 0 3px 6px rgba(0,0,0,0.4), 0 3px 6px rgba(0,0,0,0.5);
    --shadow-lg: 0 10px 20px rgba(0,0,0,0.5), 0 6px 6px rgba(0,0,0,0.6);
    --shadow-xl: 0 14px 28px rgba(0,0,0,0.6), 0 10px 10px rgba(0,0,0,0.7);
}

/* Custom Material Design Components */
.material-card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300;
}

.dark .material-card {
    @apply border border-gray-700;
}

/*
 * Material Button System
 *
 * Usage:
 * - Base class: .material-button (required)
 * - Size variant: .material-button-{xs|sm|md|lg|xl} (optional, defaults to md)
 * - Color variant: .material-button-{primary|secondary|success|danger|warning} (required)
 * - Always add: flex items-center (for consistent alignment)
 *
 * Icon Sizes (automatically applied):
 * - XS: 14px icons
 * - SM: 16px icons
 * - MD: 18px icons
 * - LG: 20px icons
 * - XL: 22px icons
 *
 * Examples:
 * - <button class="material-button material-button-sm material-button-primary flex items-center">
 *     <i class="material-icons mr-2">save</i>Small Primary
 *   </button>
 * - <a class="material-button material-button-lg material-button-secondary flex items-center">Large Secondary</a>
 */

/* Base Material Button Styles */
.material-button {
    @apply rounded-md font-medium text-sm uppercase tracking-wide transition-all duration-200 focus:outline-none cursor-pointer flex items-center justify-center;
    border: 1px solid transparent;
    line-height: 1.25rem;
    transition: all 0.2s ease;
    padding: 0.5rem 1rem; /* py-2 px-4 - Standard size (md) */
}

.material-button:hover {
    border-color: #d1d5db;
}

.material-button:focus {
    outline: none;
}

/* Button Size Variants */
.material-button-xs {
    @apply text-xs;
    padding: 0.25rem 0.75rem; /* py-1 px-3 - Extra small */
}

.material-button-sm {
    @apply text-xs;
    padding: 0.375rem 0.875rem; /* py-1.5 px-3.5 - Small */
}

.material-button-md {
    @apply text-sm;
    padding: 0.5rem 1rem; /* py-2 px-4 - Medium (default) */
}

.material-button-lg {
    @apply text-sm;
    padding: 0.625rem 1.25rem; /* py-2.5 px-5 - Large */
}

.material-button-xl {
    @apply text-base;
    padding: 0.75rem 1.5rem; /* py-3 px-6 - Extra large */
}

/* Button Color Variants */
.material-button-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:outline-none;
    border-color: #2563eb;
}

.material-button-primary:hover {
    background-color: #2563eb;
    border-color: #1d4ed8;
}

.material-button-secondary {
    @apply bg-gray-200 text-gray-800 hover:bg-gray-300 focus:outline-none;
    border-color: #e5e7eb;
}

.material-button-secondary:hover {
    background-color: #e5e7eb;
    border-color: #d1d5db;
}

.material-button-success {
    @apply bg-green-600 text-white hover:bg-green-700 focus:outline-none;
    border-color: #16a34a;
}

.material-button-success:hover {
    background-color: #16a34a;
    border-color: #15803d;
}

.material-button-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:outline-none;
    border-color: #dc2626;
}

.material-button-danger:hover {
    background-color: #dc2626;
    border-color: #b91c1c;
}

.material-button-warning {
    @apply bg-yellow-500 text-white hover:bg-yellow-600 focus:outline-none;
    border-color: #eab308;
}

.material-button-warning:hover {
    background-color: #eab308;
    border-color: #ca8a04;
}

/* Icon Size Adjustments for Button Sizes */
.material-button-xs .material-icons {
    font-size: 14px;
}

.material-button-sm .material-icons {
    font-size: 16px;
}

.material-button-md .material-icons {
    font-size: 18px;
}

.material-button-lg .material-icons {
    font-size: 20px;
}

.material-button-xl .material-icons {
    font-size: 22px;
}

.material-input {
    @apply w-full border border-gray-300 rounded-md focus:outline-none transition-all duration-200 placeholder-gray-400 text-gray-900;
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    color: #374151;
    background-color: #ffffff;
    transition: all 0.2s ease;
}

.material-input:hover {
    border-color: #d1d5db;
}

.material-input:focus {
    outline: none;
    border-color: #d1d5db;
}

.material-input-sm {
    @apply w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none transition-all duration-200 placeholder-gray-400 text-gray-900;
    font-size: 0.875rem;
    line-height: 1.25rem;
    color: #374151;
    background-color: #ffffff;
    transition: all 0.2s ease;
}

.material-input-sm:hover {
    border-color: #d1d5db;
}

.material-input-sm:focus {
    outline: none;
    border-color: #d1d5db;
}

select.material-input-sm {
    @apply px-1 pr-8 appearance-none bg-no-repeat bg-right;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}



/* Input with left icon */
.material-input-with-icon {
    @apply w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none transition-all duration-200 placeholder-gray-400 text-gray-900;
}

.material-input-with-icon:hover {
    border-color: #d1d5db;
}

.material-input-with-icon:focus {
    outline: none;
    border-color: #d1d5db;
}

/* Input with both left and right icons */
.material-input-with-icons {
    @apply w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none transition-all duration-200 placeholder-gray-400 text-gray-900;
}

.material-input-with-icons:hover {
    border-color: #d1d5db;
}

.material-input-with-icons:focus {
    outline: none;
    border-color: #d1d5db;
}

.material-nav-link {
    @apply text-gray-700 dark:text-gray-200 hover:text-blue-600 dark:hover:text-blue-400 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200;
}

.material-nav-link:hover {
    background-color: #f3f4f6;
}

.dark .material-nav-link:hover {
    background-color: #374151;
}

.material-nav-link-active {
    @apply text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20;
}

/* Material Design Form Elements - Additional Styles */
.material-select {
    @apply w-full border border-gray-300 rounded-md focus:outline-none transition-all duration-200 text-gray-900;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding: 0.25rem 2.5rem 0.25rem 0.5rem;
}

.material-select:focus {
    outline: none;
    border-color: #d1d5db;
}

.material-select-sm {
    @apply w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none transition-all duration-200 text-gray-900;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

.material-select-sm:focus {
    outline: none;
    border-color: #d1d5db;
}

.material-select-with-icon {
    @apply w-full pl-10 pr-8 py-2 border border-gray-300 rounded-md focus:outline-none transition-all duration-200 text-gray-900;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
}

.material-select-with-icon:focus {
    outline: none;
    border-color: #d1d5db;
}

.material-textarea {
    @apply w-full border border-gray-300 rounded-md focus:outline-none transition-all duration-200 placeholder-gray-400 text-gray-900;
    padding: 0.5rem;
    min-height: 5rem;
    resize: vertical;
}

.material-textarea:focus {
    outline: none;
    border-color: #d1d5db;
}

.material-textarea-sm {
    @apply w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none transition-all duration-200 placeholder-gray-400 text-gray-900;
    min-height: 3rem;
    resize: vertical;
}

.material-textarea-sm:focus {
    outline: none;
    border-color: #d1d5db;
}

/* Disabled state */
.material-input:disabled,
.material-select:disabled,
.material-textarea:disabled,
.material-button:disabled {
    @apply opacity-50 cursor-not-allowed;
}

/* Error state */
.material-input-error,
.material-select-error,
.material-textarea-error {
    @apply border-red-500;
}

.material-input-error:focus,
.material-select-error:focus,
.material-textarea-error:focus {
    outline: none;
    border-color: #ef4444;
}

/* Success state */
.material-input-success,
.material-select-success,
.material-textarea-success {
    @apply border-green-500;
}

.material-input-success:focus,
.material-select-success:focus,
.material-textarea-success:focus {
    outline: none;
    border-color: #22c55e;
}

/* Form Group */
.form-group {
    @apply mb-4;
}

.form-group label {
    @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-group .helper-text {
    @apply mt-1 text-xs text-gray-500;
}

.form-group .error-text {
    @apply mt-1 text-xs text-red-500;
}

/* Form Layout */
.form-row {
    @apply flex flex-wrap -mx-2;
}

.form-col {
    @apply px-2;
}

.form-col-6 {
    @apply w-1/2;
}

.form-col-4 {
    @apply w-1/3;
}

.form-col-3 {
    @apply w-1/4;
}

/* Form Actions */
.form-actions {
    @apply mt-6 flex items-center justify-end space-x-3;
}

/* Checkbox and Radio */
.material-checkbox,
.material-radio {
    @apply h-4 w-4 text-blue-600 focus:outline-none border-gray-300 rounded;
}

.material-radio {
    @apply rounded-full;
}

.checkbox-label,
.radio-label {
    @apply ml-2 block text-sm text-gray-700;
}

/* Input Group */
.input-group {
    @apply relative flex items-stretch w-full;
}

.input-group-text {
    @apply flex items-center px-3 py-2 text-sm font-normal text-gray-700 text-center bg-gray-100 border border-gray-300 rounded-l-md;
}

.input-group .material-input {
    @apply flex-1 min-w-0 rounded-none rounded-r-md;
}

.input-group-append .input-group-text {
    @apply rounded-l-none rounded-r-md;
}

.input-group-prepend .input-group-text {
    @apply rounded-r-none rounded-l-md;
}

/* File Input */
.material-file-input {
    @apply block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100;
}

/* Aspect Ratio Utilities for News Layout */
.aspect-w-16 {
    position: relative;
    padding-bottom: calc(var(--tw-aspect-h) / var(--tw-aspect-w) * 100%);
    --tw-aspect-w: 16;
}

.aspect-h-9 {
    --tw-aspect-h: 9;
}

.aspect-w-16 > * {
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}

/* Responsive adjustments for very small screens */
@media (max-width: 480px) {
    /* Adjust article layout for very small screens */
    .recent-article {
        padding: 1rem 0;
    }

    .recent-article .article-image {
        width: 35%;
    }

    .recent-article .article-content {
        margin-left: 0.75rem;
    }

    .recent-article h3 {
        font-size: 1rem !important;
        line-height: 1.4 !important;
    }

    .recent-article .author-info {
        font-size: 0.75rem;
    }
}

/* Tablet adjustments */
@media (min-width: 481px) and (max-width: 1023px) {
    .recent-article h3 {
        font-size: 1.125rem; /* 18px for tablets */
        line-height: 1.4;
    }
}

/* Logo and Image Placeholder Styles */
.logo-container {
    position: relative;
    overflow: hidden;
}

.logo-image {
    transition: opacity 0.3s ease;
}

.logo-text {
    transition: opacity 0.3s ease;
}

/* Image placeholder animations */
.image-placeholder {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Custom mega menu responsive widths - only needed for responsive behavior */
@media (max-width: 1024px) {
    [data-mega-content] {
        width: calc(100vw - 2rem) !important;
        max-width: 600px !important;
    }
}

@media (min-width: 1280px) {
    [data-mega-menu="technology"] [data-mega-content] {
        width: 850px !important;
    }

    [data-mega-menu="business"] [data-mega-content],
    [data-mega-menu="science"] [data-mega-content] {
        width: 650px !important;
    }

    [data-mega-menu="more"] [data-mega-content] {
        width: 450px !important;
    }
}

/* Only keep essential custom styles that can't be done with Tailwind */

/* Mobile section toggle active state with border */
[data-section].active {
    border-left-color: theme('colors.blue.500') !important;
}

/* Mobile section content active state */
[data-content].active {
    max-height: 24rem !important; /* equivalent to max-h-96 */
}

/* Mobile section arrow rotation */
.rotated {
    transform: rotate(180deg) !important;
}

/* Improve image loading states */
img[data-loaded="false"] {
    opacity: 0;
    transition: opacity 0.3s ease;
}

img[data-loaded="true"] {
    opacity: 1;
}

/* Line clamp utility for news titles */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.4;
}

/* Ensure line-clamp works in all browsers */
@supports not (-webkit-line-clamp: 2) {
    .line-clamp-2 {
        display: block;
        max-height: calc(1.4em * 2); /* line-height * number of lines */
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

/* Desktop specific adjustments for news layout */
@media (min-width: 1024px) {
    /* Reduce font sizes for better fit in 25% sidebar */
    .recent-article h3 {
        font-size: 0.875rem; /* 14px */
        line-height: 1.3;
    }

    .recent-article .article-content {
        margin-left: 0.75rem; /* Reduce margin for tighter layout */
    }

    /* Adjust category tags */
    .recent-article .text-xs {
        font-size: 0.75rem;
    }

    /* Adjust date text */
    .recent-article .text-sm {
        font-size: 0.75rem;
    }
}

/* Category Cards Horizontal Scroll */
.scrollbar-hide::-webkit-scrollbar {
    display: none;
}

.scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.category-card {
    transition: transform 0.3s ease;
}

.category-card:hover {
    transform: scale(1.05);
}

/* Responsive adjustments for trending topics */
@media (max-width: 640px) {
    .trending-topic-label span {
        font-size: 0.75rem;
        padding: 0.375rem 0.5rem;
    }
}

@media (min-width: 641px) and (max-width: 768px) {
    .trending-topic-label span {
        font-size: 0.8125rem;
        padding: 0.4375rem 0.625rem;
    }
}

/* Featured Article Overlay Styles */
.featured-article-overlay {
    background: linear-gradient(to top, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.2) 50%, transparent 100%);
    transition: background 0.3s ease;
}

.featured-article-overlay:hover {
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.3) 50%, transparent 100%);
}

/* Search Modal Styles */
#search-modal {
    backdrop-filter: blur(4px);
}

/* Search Loading Spinner */
.search-loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Smooth transitions for search states */
#search-placeholder,
#search-loading,
#search-results-list,
#no-results {
    transition: opacity 0.2s ease-in-out;
}

/* Loading state fade-in animation */
#search-loading {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Ensure modal is properly centered on all screen sizes */
@media (max-width: 640px) {
    #search-modal .relative {
        margin: 1rem;
        max-width: calc(100vw - 2rem);
    }
}

/* Responsive adjustments for featured article overlay */
@media (max-width: 640px) {
    .featured-article-overlay .overlay-content {
        padding: 1rem;
    }

    .featured-article-overlay h2 {
        font-size: 1.25rem;
        line-height: 1.4;
    }

    .featured-article-overlay p {
        font-size: 0.875rem;
        line-height: 1.5;
    }

    .featured-article-overlay .category-badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.5rem;
    }
}

@media (min-width: 641px) and (max-width: 768px) {
    .featured-article-overlay .overlay-content {
        padding: 1.5rem;
    }

    .featured-article-overlay h2 {
        font-size: 1.5rem;
        line-height: 1.3;
    }

    .featured-article-overlay p {
        font-size: 0.9375rem;
        line-height: 1.6;
    }
}

@media (min-width: 769px) {
    .featured-article-overlay .overlay-content {
        padding: 2rem;
    }

    .featured-article-overlay h2 {
        font-size: 2rem;
        line-height: 1.2;
    }
}

@media (min-width: 1024px) {
    .featured-article-overlay h2 {
        font-size: 2.5rem;
        line-height: 1.1;
    }
}




/* Swiper categories slider */
.categories-slider .swiper-slide {
    width: auto;
}

.categories-slider .swiper-wrapper {
    transition-timing-function: linear !important;
}



