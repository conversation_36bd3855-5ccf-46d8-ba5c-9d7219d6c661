<?php

namespace App\Helpers;

class ViewHelpers
{
    /**
     * Generate random color classes for categories with dark mode support
     * 
     * @return array Array with text and hover color classes
     */
    public static function getRandomCategoryColor()
    {
        $colors = [
            ['text' => 'text-red-700 dark:text-red-400', 'hover' => 'hover:text-red-600 dark:hover:text-red-300'],
            ['text' => 'text-blue-700 dark:text-blue-400', 'hover' => 'hover:text-blue-600 dark:hover:text-blue-300'],
            ['text' => 'text-green-700 dark:text-green-400', 'hover' => 'hover:text-green-600 dark:hover:text-green-300'],
            ['text' => 'text-purple-700 dark:text-purple-400', 'hover' => 'hover:text-purple-600 dark:hover:text-purple-300'],
            ['text' => 'text-indigo-700 dark:text-indigo-400', 'hover' => 'hover:text-indigo-600 dark:hover:text-indigo-300'],
            ['text' => 'text-pink-700 dark:text-pink-400', 'hover' => 'hover:text-pink-600 dark:hover:text-pink-300'],
            ['text' => 'text-yellow-700 dark:text-yellow-400', 'hover' => 'hover:text-yellow-600 dark:hover:text-yellow-300'],
            ['text' => 'text-teal-700 dark:text-teal-400', 'hover' => 'hover:text-teal-600 dark:hover:text-teal-300'],
            ['text' => 'text-orange-700 dark:text-orange-400', 'hover' => 'hover:text-orange-600 dark:hover:text-orange-300'],
            ['text' => 'text-cyan-700 dark:text-cyan-400', 'hover' => 'hover:text-cyan-600 dark:hover:text-cyan-300'],
            ['text' => 'text-emerald-700 dark:text-emerald-400', 'hover' => 'hover:text-emerald-600 dark:hover:text-emerald-300'],
            ['text' => 'text-violet-700 dark:text-violet-400', 'hover' => 'hover:text-violet-600 dark:hover:text-violet-300'],
            ['text' => 'text-rose-700 dark:text-rose-400', 'hover' => 'hover:text-rose-600 dark:hover:text-rose-300'],
            ['text' => 'text-amber-700 dark:text-amber-400', 'hover' => 'hover:text-amber-600 dark:hover:text-amber-300'],
            ['text' => 'text-lime-700 dark:text-lime-400', 'hover' => 'hover:text-lime-600 dark:hover:text-lime-300'],
        ];
        return $colors[array_rand($colors)];
    }
}
