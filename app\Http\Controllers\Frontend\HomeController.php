<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\News;
use App\Models\Category;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    public function index()
    {
        // Get only the latest featured news
        $featuredNews = News::published()
            ->latest('published_at')
            ->limit(1)
            ->get();
            
        // Get top stories by view count
        $topStories = News::published()
            ->orderBy('view_count', 'desc')
            ->limit(4)
            ->get();
            
        // Get initial recent news (excluding featured news and top stories)
        $recentNews = News::published()
            ->whereNotIn('id', $featuredNews->pluck('id')->merge($topStories->pluck('id')))
            ->orderBy('published_at', 'desc')
            ->limit(20)
            ->get();

        // Get initial recent news (excluding featured news and top stories)
        $indiaNews = News::published()
            ->whereNotIn('id', $featuredNews->pluck('id')->merge($topStories->pluck('id')))
            ->whereHas('tags', function ($query) {
                $query->where('name', 'ahmedabad');
            })
            ->orderBy('published_at', 'desc')
            ->limit(4)
            ->get();
            
        // Get all active categories - using status column instead of active
        $categories = Category::where('status', 'active')
            ->orderBy('name')
            ->get();
            
        return view('frontend.home', compact('featuredNews', 'topStories', 'recentNews', 'categories','indiaNews'));
    }

    public function loadMoreNews(Request $request)
    {
        $offset = $request->input('offset', 0);
        $limit = $request->input('limit', 10);
        
        // Get featured and top stories IDs to exclude
        $featuredIds = News::published()->featured()->pluck('id');
        $topStoriesIds = News::published()->orderBy('view_count', 'desc')->limit(5)->pluck('id');
        $excludeIds = $featuredIds->merge($topStoriesIds);
        
        // Get more recent news
        $moreNews = News::published()
            ->whereNotIn('id', $excludeIds)
            ->orderBy('published_at', 'desc')
            ->skip($offset)
            ->take($limit)
            ->get();
            
        if ($moreNews->isEmpty()) {
            return response()->json(['html' => '', 'hasMore' => false]);
        }
        
        $html = view('frontend.news.news-items', ['moreNews' => $moreNews])->render();
        
        return response()->json([
            'html' => $html,
            'hasMore' => $moreNews->count() >= $limit
        ]);
    }
}