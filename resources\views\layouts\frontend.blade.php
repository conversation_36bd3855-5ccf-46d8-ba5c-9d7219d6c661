<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="light">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }} - @yield('title', 'Welcome')</title>

    <!-- Roboto font is now loaded locally via CSS @font-face declarations -->

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    @stack('styles')
</head>

<body class="bg-gray-50 dark:bg-gray-900 font-sans antialiased transition-colors duration-200">
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        @include('components.frontend.header')

        <!-- Main Content -->
        <main class="flex-1">
            @yield('content')
        </main>

        <!-- Footer -->
        @include('components.frontend.footer')
    </div>

    @stack('scripts')

    <!-- Frontend Layout JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile slide menu functionality
            const mobileNavButton = document.getElementById('mobile-nav-button');
            const mobileSlideMenu = document.getElementById('mobile-slide-menu');
            const mobileMenuOverlay = document.getElementById('mobile-menu-overlay');
            const mobileMenuClose = document.getElementById('mobile-menu-close');

            function openMobileMenu() {
                if (mobileSlideMenu && mobileMenuOverlay) {
                    // Show overlay and menu (don't hide scrollbar to prevent layout shift)
                    mobileMenuOverlay.classList.remove('opacity-0', 'pointer-events-none');
                    mobileMenuOverlay.classList.add('opacity-100');

                    mobileSlideMenu.classList.remove('-translate-x-full');
                    mobileSlideMenu.classList.add('translate-x-0');

                    mobileNavButton.setAttribute('aria-expanded', 'true');
                }
            }

            function closeMobileMenu() {
                if (mobileSlideMenu && mobileMenuOverlay) {
                    // Hide menu with animation
                    mobileSlideMenu.classList.remove('translate-x-0');
                    mobileSlideMenu.classList.add('-translate-x-full');

                    mobileMenuOverlay.classList.remove('opacity-100');
                    mobileMenuOverlay.classList.add('opacity-0', 'pointer-events-none');

                    mobileNavButton.setAttribute('aria-expanded', 'false');
                }
            }

            // Mobile menu event listeners
            if (mobileNavButton) {
                mobileNavButton.addEventListener('click', openMobileMenu);
            }

            if (mobileMenuClose) {
                mobileMenuClose.addEventListener('click', closeMobileMenu);
            }

            if (mobileMenuOverlay) {
                mobileMenuOverlay.addEventListener('click', closeMobileMenu);
            }

            // Mobile menu accordion functionality
            const accordionButtons = document.querySelectorAll('[data-section]');

            accordionButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const section = this.getAttribute('data-section');
                    const content = document.querySelector(`[data-content="${section}"]`);
                    const arrow = this.querySelector('svg:last-child');

                    if (content) {
                        const isOpen = content.style.maxHeight && content.style.maxHeight !== '0px';

                        // Close all other accordion sections first
                        accordionButtons.forEach(otherButton => {
                            const otherSection = otherButton.getAttribute('data-section');
                            const otherContent = document.querySelector(`[data-content="${otherSection}"]`);
                            const otherArrow = otherButton.querySelector('svg:last-child');

                            if (otherSection !== section && otherContent) {
                                otherContent.style.maxHeight = '0px';
                                if (otherArrow) {
                                    otherArrow.style.transform = 'rotate(0deg)';
                                }
                            }
                        });

                        if (isOpen) {
                            // Close current section
                            content.style.maxHeight = '0px';
                            if (arrow) {
                                arrow.style.transform = 'rotate(0deg)';
                            }
                        } else {
                            // Open current section
                            content.style.maxHeight = content.scrollHeight + 'px';
                            if (arrow) {
                                arrow.style.transform = 'rotate(180deg)';
                            }
                        }
                    }
                });
            });

            // Keyboard support for mobile menu
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    // Close mobile menu if open
                    if (mobileSlideMenu && !mobileSlideMenu.classList.contains('-translate-x-full')) {
                        closeMobileMenu();
                    }
                }
            });

            // Frontend dropdown functionality
            const frontendDropdowns = document.querySelectorAll('[data-frontend-dropdown]');
            frontendDropdowns.forEach(dropdown => {
                const button = dropdown.querySelector('[data-frontend-dropdown-button]');
                const menu = dropdown.querySelector('[data-frontend-dropdown-menu]');

                if (button && menu) {
                    let isOpen = false;

                    button.addEventListener('click', function(e) {
                        e.stopPropagation();
                        isOpen = !isOpen;

                        if (isOpen) {
                            menu.classList.remove('hidden');
                        } else {
                            menu.classList.add('hidden');
                        }
                    });

                    // Close on outside click
                    document.addEventListener('click', function() {
                        if (isOpen) {
                            isOpen = false;
                            menu.classList.add('hidden');
                        }
                    });
                }
            });

            // Search popup functionality
            const searchButton = document.getElementById('search-button');
            const searchModal = document.getElementById('search-modal');
            const closeSearchModal = document.getElementById('close-search-modal');
            const searchOverlay = document.getElementById('search-overlay');
            const searchInput = document.getElementById('search-input');
            const searchForm = document.getElementById('search-form');
            const searchPlaceholder = document.getElementById('search-placeholder');
            const searchLoading = document.getElementById('search-loading');
            const searchResultsList = document.getElementById('search-results-list');
            const noResults = document.getElementById('no-results');

            // Open search modal with slide-from-top animation
            function openModal() {
                if (searchModal && searchModal.classList.contains('hidden')) {
                    const modalPanel = document.getElementById('search-modal-panel');

                    // Show modal (don't hide scrollbar to prevent layout shift)
                    searchModal.classList.remove('hidden');

                    // Trigger slide-from-top animation using Tailwind classes
                    setTimeout(() => {
                        if (modalPanel) {
                            modalPanel.classList.remove('-translate-y-4', 'opacity-0');
                            modalPanel.classList.add('translate-y-0', 'opacity-100');
                        }
                    }, 10); // Small delay to ensure DOM is ready

                    // Focus on search input after animation starts
                    // setTimeout(() => {
                    //     searchInput.focus();
                    // }, 100);
                }
            }

            if (searchButton && searchModal) {
                searchButton.addEventListener('click', openModal);
            }



            // Close search modal with slide-to-top animation
            function closeModal() {
                if (searchModal && !searchModal.classList.contains('hidden')) {
                    const modalPanel = document.getElementById('search-modal-panel');

                    // Clear any pending search timeout
                    if (searchTimeout) {
                        clearTimeout(searchTimeout);
                    }

                    // Trigger slide-to-top animation using Tailwind classes
                    if (modalPanel) {
                        modalPanel.classList.remove('translate-y-0', 'opacity-100');
                        modalPanel.classList.add('-translate-y-4', 'opacity-0');
                    }

                    // Wait for animation to complete before hiding
                    // setTimeout(() => {
                    //     searchModal.classList.add('hidden');
                    //     searchInput.value = '';
                    //     showPlaceholder();

                    //     // Reset modal panel for next time
                    //     if (modalPanel) {
                    //         modalPanel.classList.remove('translate-y-0', 'opacity-100');
                    //         modalPanel.classList.add('-translate-y-4', 'opacity-0');
                    //     }
                    // }, 300); // Wait for Tailwind transition duration
                }
            }

            if (closeSearchModal) {
                closeSearchModal.addEventListener('click', closeModal);
            }

            if (searchOverlay) {
                searchOverlay.addEventListener('click', closeModal);
            }

            // Close modal on Escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !searchModal.classList.contains('hidden')) {
                    closeModal();
                }
            });

            // Search functionality
            function showPlaceholder() {
                searchPlaceholder.classList.remove('hidden');
                searchLoading.classList.add('hidden');
                searchResultsList.classList.add('hidden');
                noResults.classList.add('hidden');
            }

            function showLoading() {
                searchPlaceholder.classList.add('hidden');
                searchLoading.classList.remove('hidden');
                searchResultsList.classList.add('hidden');
                noResults.classList.add('hidden');
            }

            function showResults(data) {
                searchPlaceholder.classList.add('hidden');
                searchLoading.classList.add('hidden');
                
                if (data.count > 0) {
                    noResults.classList.add('hidden');
                    searchResultsList.classList.remove('hidden');
                    searchResultsList.innerHTML = data.html;
                } else {
                    searchResultsList.classList.add('hidden');
                    noResults.classList.remove('hidden');
                }
            }

            function showNoResults() {
                searchPlaceholder.classList.add('hidden');
                searchLoading.classList.add('hidden');
                searchResultsList.classList.add('hidden');
                noResults.classList.remove('hidden');
            }

            // Variable to store search timeout
            let searchTimeout;

            function performSearch(query) {
                // Clear any existing timeout
                if (searchTimeout) {
                    clearTimeout(searchTimeout);
                }

                if (!query.trim()) {
                    showPlaceholder();
                    return;
                }

                // Show loading state immediately
                showLoading();

                // Delay the search slightly to avoid too many requests while typing
                searchTimeout = setTimeout(() => {
                    // Get CSRF token
                    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
                    
                    // Fetch results from the server
                    fetch('{{ route("search") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': csrfToken,
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify({ query: query })
                    })
                    .then(response => response.json())
                    .then(data => {
                        showResults(data);
                    })
                    .catch(error => {
                        console.error('Search error:', error);
                        showNoResults();
                    });
                }, 300);
            }

            // Search input event listener
            if (searchInput) {
                searchInput.addEventListener('input', function(e) {
                    const query = e.target.value;
                    performSearch(query);
                });
            }

            // Prevent form submission
            if (searchForm) {
                searchForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                });
            }

            // Logo image loading functionality
            const logoImage = document.querySelector('.logo-image');
            const logoText = document.querySelector('.logo-text');

            if (logoImage && logoText) {
                // Try to load the logo image
                logoImage.onload = function() {
                    logoImage.classList.remove('hidden');
                    logoText.style.display = 'none';
                };

                logoImage.onerror = function() {
                    logoImage.style.display = 'none';
                    logoText.style.display = 'block';
                };

                // Check if image is already loaded (cached)
                if (logoImage.complete) {
                    if (logoImage.naturalWidth > 0) {
                        logoImage.classList.remove('hidden');
                        logoText.style.display = 'none';
                    } else {
                        logoImage.style.display = 'none';
                        logoText.style.display = 'block';
                    }
                }
            }

            // Enhanced image loading for all images
            const images = document.querySelectorAll('img[data-src]');
            images.forEach(img => {
                img.setAttribute('data-loaded', 'false');

                img.onload = function() {
                    img.setAttribute('data-loaded', 'true');
                };

                img.onerror = function() {
                    img.setAttribute('data-loaded', 'false');
                    // Show placeholder if available
                    const placeholder = img.nextElementSibling;
                    if (placeholder && placeholder.classList.contains('image-placeholder')) {
                        img.style.display = 'none';
                        placeholder.style.display = 'flex';
                        placeholder.classList.add('image-placeholder');
                    }
                };
            });
        });
    </script>
</body>

</html>
