<?php

namespace App\Services;

use App\Models\LatestNewsTitle;
use App\Models\News;
use App\Models\Category;
use App\Models\Tag;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Exception;

class NewsGeneratorService
{
    protected $geminiService;

    public function __construct()
    {
        // Use the cURL version for better SSL compatibility
        $this->geminiService = new GeminiServiceCurl();
    }

    /**
     * Generate news articles from latest news titles
     *
     * @param int $limit Maximum number of articles to generate
     * @return array
     */
    public function generateNewsFromLatestTitles(int $limit = 10): array
    {
        $results = [
            'processed' => 0,
            'successful' => 0,
            'failed' => 0,
            'errors' => []
        ];

        try {
            $latestNews = LatestNewsTitle::readyForGeneration()
                ->limit($limit)
                ->orderby('id','desc')
                ->get();

            Log::info('Starting news generation process', [
                'count' => $latestNews->count(),
                'limit' => $limit
            ]);

            foreach ($latestNews as $newsTitle) {
                $results['processed']++;

                try {
                    $this->generateSingleNews($newsTitle);
                    $results['successful']++;

                    Log::info('Successfully generated news', [
                        'id' => $newsTitle->id,
                        'title' => $newsTitle->title
                    ]);
                } catch (Exception $e) {
                    $results['failed']++;
                    $results['errors'][] = [
                        'id' => $newsTitle->id,
                        'title' => $newsTitle->title,
                        'error' => $e->getMessage()
                    ];

                    Log::error('Failed to generate news', [
                        'id' => $newsTitle->id,
                        'title' => $newsTitle->title,
                        'error' => $e->getMessage()
                    ]);
                }
            }
        } catch (Exception $e) {
            Log::error('News generation process failed', ['error' => $e->getMessage()]);
            $results['errors'][] = ['general' => $e->getMessage()];
        }

        return $results;
    }

    /**
     * Generate content for a single news title (public method)
     */
    public function generateSingleNewsFromTitle(LatestNewsTitle $newsTitle): ?News
    {
        try {
            if ($newsTitle->is_generated) {
                Log::warning('News title already generated', ['id' => $newsTitle->id]);
                return null;
            }

            $this->generateSingleNews($newsTitle);

            // Return the generated news
            return $newsTitle->fresh()->generatedNews;
        } catch (\Exception $e) {
            Log::error('Failed to generate single news', [
                'id' => $newsTitle->id,
                'title' => $newsTitle->title,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Generate a single news article from LatestNewsTitle
     */
    protected function generateSingleNews(LatestNewsTitle $newsTitle): void
    {
        // Generate unique content using Gemini
        $generatedContent = $this->geminiService->generateUniqueNews(
            $newsTitle->title,
            $newsTitle->description
        );

        // Create the news article
        $news = $this->createNewsArticle($newsTitle, $generatedContent);

        // Attach category provided by Gemini and generate tags
        $category = $generatedContent['category'] ?? 'General';
        $this->attachCategoriesAndTags($news, $category);

        // Mark the latest news title as generated
        $newsTitle->markAsGenerated($news->id);
    }

    /**
     * Create news article from generated content
     */
    protected function createNewsArticle(LatestNewsTitle $newsTitle, array $generatedContent): News
    {
        $newsData = [
            'title' => $generatedContent['title'],
            'description' => $generatedContent['description'],
            'status' => 'active',
            'approval_status' => 'approved',
            'sort_order' => 0,
            'approved_at' => now(),
            'is_featured' => false,
            'meta_title' => $generatedContent['meta_title'] ?? $generatedContent['title'],
            'meta_description' => $generatedContent['meta_description'] ?? null,
            'meta_keywords' => $generatedContent['meta_keywords'] ?? null,
            'robots_meta' => 'index,follow',
            'og_title' => $generatedContent['og_title'] ?? $generatedContent['title'],
            'og_description' => $generatedContent['og_description'] ?? null,
            'og_type' => 'article',
        ];

        // Generate a placeholder image for now
        $newsData['main_image'] = $this->generatePlaceholderImage($generatedContent['title']);

        return News::create($newsData);
    }

    /**
     * Attach categories and tags to the news article
     */
    protected function attachCategoriesAndTags(News $news, string $categoryName): void
    {
        // Find or create category provided by Gemini
        $category = $this->findOrCreateCategory($categoryName);
        if ($category) {
            $news->categories()->attach($category->id);
        }

        // Generate and attach tags based on content
        $tags = $this->generateTags($news->title, $news->description, $categoryName);
        if (!empty($tags)) {
            $news->tags()->attach($tags);
        }
    }

    /**
     * Find or create category
     */
    protected function findOrCreateCategory(string $categoryName): ?Category
    {
        try {
            // First try to find existing category
            $category = Category::where('name', 'LIKE', '%' . $categoryName . '%')
                ->orWhere('slug', Str::slug($categoryName))
                ->first();

            if ($category) {
                return $category;
            }

            // Create new category if not found
            return Category::create([
                'name' => $categoryName,
                'slug' => Str::slug($categoryName),
                'description' => 'Auto-generated category for ' . $categoryName,
                'color' => $this->generateRandomColor(),
                'status' => 'active',
                'sort_order' => 0,
                'is_featured' => false,
            ]);
        } catch (Exception $e) {
            Log::warning('Failed to create category', [
                'category' => $categoryName,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Generate tags for the news article
     */
    protected function generateTags(string $title, string $description, string $category): array
    {
        try {
            // Extract keywords from title and description
            $keywords = $this->extractKeywords($title . ' ' . $description);

            // Add category as a tag
            $keywords[] = $category;

            $tagIds = [];

            foreach (array_unique($keywords) as $keyword) {
                if (strlen($keyword) < 3) continue;

                $tag = Tag::firstOrCreate(
                    ['slug' => Str::slug($keyword)],
                    [
                        'name' => $keyword,
                        'description' => 'Auto-generated tag',
                        'color' => $this->generateRandomColor(),
                        'status' => 'active',
                        'sort_order' => 0,
                    ]
                );

                $tagIds[] = $tag->id;
            }

            return array_slice($tagIds, 0, 5); // Limit to 5 tags

        } catch (Exception $e) {
            Log::warning('Failed to generate tags', ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Extract keywords from text
     */
    protected function extractKeywords(string $text): array
    {
        // Remove common stop words
        $stopWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'];

        // Extract words
        $words = str_word_count(strtolower($text), 1);

        // Filter out stop words and short words
        $keywords = array_filter($words, function ($word) use ($stopWords) {
            return strlen($word) >= 3 && !in_array($word, $stopWords);
        });

        // Get most frequent words
        $wordCounts = array_count_values($keywords);
        arsort($wordCounts);

        return array_keys(array_slice($wordCounts, 0, 10));
    }

    /**
     * Generate a placeholder image
     */
    protected function generatePlaceholderImage(string $title): string
    {
        try {
            // Create filename with proper extension
            $filename = 'news/main-images/placeholder-' . Str::slug($title) . '-' . time() . '.svg';

            // Create SVG placeholder with gradient and logo
            $this->createSvgPlaceholder($filename, $title);

            return $filename;
        } catch (Exception $e) {
            Log::warning('Failed to generate placeholder image', ['error' => $e->getMessage()]);
            // Return a default placeholder path
            return 'news/main-images/default-placeholder.png';
        }
    }

    /**
     * Create a simple placeholder image
     */
    protected function createSimplePlaceholder(string $filename, string $title): void
    {
        try {
            // Check if GD extension is available
            if (!extension_loaded('gd')) {
                Log::warning('GD extension not available, using external placeholder service');
                $this->downloadPlaceholderImage($filename, $title);
                return;
            }

            // Create a simple image using GD
            $width = 800;
            $height = 400;

            // Create image
            $image = imagecreatetruecolor($width, $height);

            // Define colors
            $backgroundColor = imagecolorallocate($image, 229, 231, 235); // #f3f4f6

            $colors = [
                ['name' => 'red',     'rgb' => [185, 28, 28]],
                ['name' => 'blue',    'rgb' => [29, 78, 216]],
                ['name' => 'green',   'rgb' => [21, 128, 61]],
                ['name' => 'purple',  'rgb' => [126, 34, 206]],
                ['name' => 'indigo',  'rgb' => [67, 56, 202]],
                ['name' => 'pink',    'rgb' => [219, 39, 119]],
                ['name' => 'yellow',  'rgb' => [202, 138, 4]],
                ['name' => 'teal',    'rgb' => [13, 148, 136]],
                ['name' => 'orange',  'rgb' => [234, 88, 12]],
                ['name' => 'cyan',    'rgb' => [6, 182, 212]],
                ['name' => 'emerald', 'rgb' => [5, 150, 105]],
                ['name' => 'violet',  'rgb' => [139, 92, 246]],
                ['name' => 'rose',    'rgb' => [225, 29, 72]],
                ['name' => 'amber',   'rgb' => [245, 158, 11]],
                ['name' => 'lime',    'rgb' => [101, 163, 13]],
            ];


            $randomColor = $colors[array_rand($colors)];
            $textColor = imagecolorallocate($image, $randomColor['rgb'][0], $randomColor['rgb'][1], $randomColor['rgb'][2]);
            // $textColor = imagecolorallocate($image, 107, 114, 128); // #6b7280


            imagefill($image, 0, 0, $backgroundColor);

            // Add text using built-in font (simpler and more reliable)
            $text = substr($title, 0, 60); // Limit text length

            // Break text into multiple lines if needed
            $lines = [];
            $maxCharsPerLine = 40;
            $words = explode(' ', $text);
            $currentLine = '';

            foreach ($words as $word) {
                if (strlen($currentLine . ' ' . $word) <= $maxCharsPerLine) {
                    $currentLine .= ($currentLine ? ' ' : '') . $word;
                } else {
                    if ($currentLine) {
                        $lines[] = $currentLine;
                        $currentLine = $word;
                    } else {
                        $lines[] = substr($word, 0, $maxCharsPerLine);
                    }
                }
            }
            if ($currentLine) {
                $lines[] = $currentLine;
            }

            // Limit to 3 lines maximum
            $lines = array_slice($lines, 0, 3);

            // Calculate starting position
            $lineHeight = 20;
            $totalTextHeight = count($lines) * $lineHeight;
            $startY = ($height - $totalTextHeight) / 2;

            // Draw each line
            foreach ($lines as $i => $line) {
                $lineWidth = strlen($line) * 10; // Approximate width for font size 5
                $lineX = ($width - $lineWidth) / 2;
                $lineY = $startY + ($i * $lineHeight);
                imagestring($image, 15, $lineX, $lineY, $line, $textColor);
            }

            // Ensure directory exists
            $directory = dirname(storage_path('app/public/' . $filename));
            if (!file_exists($directory)) {
                mkdir($directory, 0755, true);
            }

            // Save image
            $fullPath = storage_path('app/public/' . $filename);
            imagepng($image, $fullPath);

            // Clean up memory
            imagedestroy($image);

            Log::info('Placeholder image created successfully', ['filename' => $filename]);
        } catch (Exception $e) {
            Log::warning('Failed to create placeholder image with GD', ['error' => $e->getMessage()]);
            // Fallback to external service
            $this->downloadPlaceholderImage($filename, $title);
        }
    }

    /**
     * Download placeholder image from external service as fallback
     */
    protected function downloadPlaceholderImage(string $filename, string $title): void
    {
        try {
            // Use a placeholder service like placeholder.com or picsum.photos
            $text = urlencode(substr($title, 0, 30));
            $url = "https://via.placeholder.com/800x400/f3f4f6/6b7280?text=" . $text;

            // Download the image
            $imageContent = file_get_contents($url);

            if ($imageContent !== false) {
                Storage::disk('public')->put($filename, $imageContent);
                Log::info('Placeholder image downloaded successfully', ['filename' => $filename]);
            } else {
                throw new Exception('Failed to download placeholder image');
            }
        } catch (Exception $e) {
            Log::warning('Failed to download placeholder image', ['error' => $e->getMessage()]);
            // Create a simple text file as last resort
            $this->createTextPlaceholder($filename, $title);
        }
    }

    /**
     * Create a simple text placeholder as last resort
     */
    protected function createTextPlaceholder(string $filename, string $title): void
    {
        try {
            // Create a simple SVG as fallback
            $svg = '<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="400" xmlns="http://www.w3.org/2000/svg">
    <rect width="800" height="400" fill="#f3f4f6"/>
    <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="20" text-anchor="middle" dy=".3em" fill="#6b7280">
        ' . htmlspecialchars(substr($title, 0, 50)) . '
    </text>
</svg>';

            // Save as SVG file
            $svgFilename = str_replace('.png', '.svg', $filename);
            Storage::disk('public')->put($svgFilename, $svg);

            Log::info('SVG placeholder created as fallback', ['filename' => $svgFilename]);
        } catch (Exception $e) {
            Log::error('All placeholder creation methods failed', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Generate a random color for categories/tags
     */
    protected function generateRandomColor(): string
    {
        $colors = [
            '#3B82F6',
            '#EF4444',
            '#10B981',
            '#F59E0B',
            '#8B5CF6',
            '#06B6D4',
            '#84CC16',
            '#F97316',
            '#EC4899',
            '#6366F1'
        ];

        return $colors[array_rand($colors)];
    }

    /**
     * Create an SVG placeholder with gradient background and logo
     */
    protected function createSvgPlaceholder(string $filename, string $title): void
    {
        try {
            // Limit text length
            $text = substr($title, 0, 100);

            // Get random colors for gradient (lighter variants)
            $colors = [
                ['name' => 'red',     'hex' => '#FEF2F2', 'dark' => '#FEE2E2'],
                ['name' => 'blue',    'hex' => '#EFF6FF', 'dark' => '#DBEAFE'],
                ['name' => 'green',   'hex' => '#F0FDF4', 'dark' => '#DCFCE7'],
                ['name' => 'purple',  'hex' => '#FAF5FF', 'dark' => '#F3E8FF'],
                ['name' => 'indigo',  'hex' => '#EEF2FF', 'dark' => '#E0E7FF'],
                ['name' => 'pink',    'hex' => '#FDF2F8', 'dark' => '#FCE7F3'],
                ['name' => 'yellow',  'hex' => '#FFFBEB', 'dark' => '#FEF3C7'],
                ['name' => 'teal',    'hex' => '#F0FDFA', 'dark' => '#CCFBF1'],
                ['name' => 'orange',  'hex' => '#FFF7ED', 'dark' => '#FFEDD5'],
                ['name' => 'emerald', 'hex' => '#ECFDF5', 'dark' => '#D1FAE5']
            ];

            $randomColor = $colors[array_rand($colors)];
            $startColor = $randomColor['hex'];
            $endColor = $randomColor['dark'];
            $logoColor = $randomColor['dark'];

            // Break text into multiple lines if needed
            $words = explode(' ', $text);
            $lines = [];
            $currentLine = '';
            $maxCharsPerLine = 40; // Increased for wider image

            foreach ($words as $word) {
                if (strlen($currentLine . ' ' . $word) <= $maxCharsPerLine) {
                    $currentLine .= ($currentLine ? ' ' : '') . $word;
                } else {
                    if ($currentLine) {
                        $lines[] = $currentLine;
                        $currentLine = $word;
                    } else {
                        $lines[] = substr($word, 0, $maxCharsPerLine);
                    }
                }
            }

            if ($currentLine) {
                $lines[] = $currentLine;
            }

            // Limit to 3 lines maximum
            $lines = array_slice($lines, 0, 3);

            // Create SVG content with text lines
            $textElements = '';
            $lineHeight = 46; // Increased for larger image
            $totalTextHeight = count($lines) * $lineHeight;
            $startY = 360 - ($totalTextHeight / 2) + ($lineHeight / 2); // Adjusted for new height

            foreach ($lines as $i => $line) {
                $yPos = $startY + ($i * $lineHeight);
                $textElements .= '    <text x="640" y="' . $yPos . '" font-family="Roboto, Arial, sans-serif" font-size="40" font-weight="700" text-anchor="middle" fill="#4B5563">' .
                    htmlspecialchars($line) . '</text>' . PHP_EOL;
            }

            // Get logo as base64
            $logoPath = public_path('storage/images/logo-color.png');
            $base64Logo = '';
            if (file_exists($logoPath)) {
                $imageData = file_get_contents($logoPath);
                $base64Logo = 'data:image/png;base64,' . base64_encode($imageData);
            } else {
                $base64Logo = 'data:image/svg+xml;base64,' . base64_encode('<svg width="90" height="65" xmlns="http://www.w3.org/2000/svg"><rect width="90" height="65" fill="' . $logoColor . '"/><text x="45" y="40" font-family="Arial, sans-serif" font-size="26" font-weight="bold" text-anchor="middle" fill="#FFFFFF">' . substr(config('app.name', 'N'), 0, 1) . '</text></svg>');
            }

            // Create SVG content with new dimensions
            $svg = '<?xml version="1.0" encoding="UTF-8"?>
<svg width="1280" height="720" xmlns="http://www.w3.org/2000/svg">
    <!-- Gradient background -->
    <defs>
        <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stop-color="' . $startColor . '" />
            <stop offset="100%" stop-color="' . $endColor . '" />
        </linearGradient>
    </defs>
    
    <!-- Background rectangle with gradient -->
    <rect width="1280" height="720" fill="url(#bg-gradient)"/>
    
    <!-- Logo in top right corner -->
    <g transform="translate(1150, 10)">
        <image href="' . $base64Logo . '" x="5" y="5" width="90" height="65" preserveAspectRatio="xMidYMid meet"/>
    </g>
    
    <!-- Centered text -->
' . $textElements . '</svg>';

            // Save SVG file
            Storage::disk('public')->put($filename, $svg);

            Log::info('SVG placeholder created successfully', ['filename' => $filename]);
        } catch (Exception $e) {
            Log::error('Failed to create SVG placeholder', ['error' => $e->getMessage()]);
        }
    }
}
