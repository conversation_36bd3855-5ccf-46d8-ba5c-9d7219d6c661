@extends('layouts.frontend')

@section('title', 'News & Stories')

@section('content')
<!-- Main Content Section -->
<section class="bg-gray-50 dark:bg-gray-700 dark:bg-gray-900 py-8 container mx-auto transition-colors duration-200">
    <div class=" px-4 sm:px-6 lg:px-8 md:flex lg:flex xl:flex lg:items-start gap-8">
        <!-- Featured Article -->
        <article class="relative sticky md:w-7/12 lg:w-8/12 xl:w-8/12">
            @if(isset($featuredNews) && $featuredNews->isNotEmpty())
            <a class="group aspect-h-9 aspect-w-16 relative block overflow-hidden rounded bg-gray-100 dark:bg-gray-800"
                href="{{ route('news.show', $featuredNews[0]->slug) }}">
                <img alt="{{ $featuredNews[0]->title }}"
                    loading="lazy"
                    decoding="async"
                    class="rounded object-cover object-center transition duration-300 ease-in-out group-hover:scale-110"
                    style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent"
                    src="{{ asset('storage/' . $featuredNews[0]->main_image) }}"
                    onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">

                <!-- Gradient Overlay -->
                <div class="featured-article-overlay absolute inset-0 rounded"></div>

                <!-- Content Overlay -->
                <div class="overlay-content absolute bottom-0 left-0 right-0 p-4 md:p-8 text-white z-10 flex flex-col justify-end">
                    <!-- Category -->
                    <div class="content">
                        @randomCategoryColor
                        @if($featuredNews[0]->categories->isNotEmpty())
                        <span class="category-badge inline-block bg-red-600 dark:bg-red-500 text-white text-sm font-medium uppercase tracking-widest px-3 py-1 rounded-md mb-3 transition-colors duration-300 hover:bg-red-700 dark:hover:bg-red-600">
                            {{ $featuredNews[0]->categories->first()->name }}
                        </span>
                        @endif

                        <!-- Title -->
                        <h2 class="text-xl md:text-2xl lg:text-2xl font-bold leading-tight text-white transition duration-300 ease-in-out group-hover:text-gray-100">
                            {{ $featuredNews[0]->title }}
                        </h2>
                    </div>
                </div>

                <!-- Image placeholder -->
                <div class="absolute inset-0 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center" style="display:none;">
                    <div class="text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                        </svg>
                        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400">Image not available</p>
                    </div>
                </div>
            </a>
            @else
            <!-- Fallback if no featured news -->
            <div class="aspect-h-9 aspect-w-16 relative block overflow-hidden rounded bg-gray-200 dark:bg-gray-800 flex items-center justify-center">
                <div class="flex items-center justify-center flex-col">
                    <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                    </svg>
                    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">No featured news available</p>
                </div>
            </div>
            @endif
        </article>
        <!-- Sidebar Section -->
        <div class="md:w-5/12 lg:w-4/12 xl:w-4/12 mt-8 md:mt-0">
            <!-- Top Stories Section -->
            <h3 class="relative border-b border-gray-300/70 dark:border-gray-600/70 pb-2.5 text-2xl font-medium text-gray-900 dark:text-white before:absolute before:-bottom-px before:left-0 before:h-px before:w-24 before:bg-red-600 dark:before:bg-red-500 before:content-[''] transition-colors duration-200 mb-4">
                Top Stories
            </h3>
            <div class="grid grid-cols-1">
                @if(isset($topStories) && $topStories->isNotEmpty())
                @foreach($topStories as $story)
                <!-- Article -->
                <article class="py-2 flex">
                    <a class="article-image w-2/5 flex-shrink-0" href="{{ route('news.show', $story->slug) }}">
                        <div class="group aspect-h-9 aspect-w-16 overflow-hidden rounded bg-gray-100 dark:bg-gray-800 relative">
                            <img alt="{{ $story->title }}"
                                loading="lazy"
                                decoding="async"
                                class="rounded object-cover object-center transition duration-300 ease-in-out group-hover:scale-110"
                                style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent"
                                src="{{ asset('storage/' . $story->main_image) }}"
                                onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <!-- Image placeholder -->
                            <div class="absolute inset-0 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center" style="display:none;">
                                <svg class="h-8 w-8 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                            </div>
                        </div>
                    </a>
                    <div class="ml-3 flex-1">
                        @randomCategoryColor
                        @if($story->categories->isNotEmpty())
                        <div class="text-xs font-medium uppercase tracking-widest {{ $categoryColor['text'] }} dark:text-blue-400 transition-colors duration-300 ease-in-out {{ $categoryColor['hover'] }} dark:hover:text-blue-300 leading-none">
                            <a href="{{ route('category.show', $story->categories->first()->slug) }}">{{ $story->categories->first()->name }}</a>
                        </div>
                        @endif
                        <a href="{{ route('news.show', $story->slug) }}">
                            <h3 class="mt-1 text-sm lg:text-sm font-medium leading-normal tracking-normal text-gray-900 dark:text-white decoration-gray-800 dark:decoration-gray-200 decoration-2 transition duration-300 ease-in-out">
                                {{ $story->title }}
                            </h3>
                        </a>
                    </div>
                </article>
                @endforeach
                @else
                <article class="recent-article py-2 flex">
                    <div class="w-full text-center py-4">
                        <p class="text-gray-500 dark:text-gray-400">No top stories available</p>
                    </div>
                </article>
                @endif
            </div>
        </div>
    </div>
</section>
<!-- Categories Section -->
 @if($categories)
<section class="bg-white dark:bg-gray-800 pt-4 md:pt-4 lg:pt-8 transition-colors duration-200">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="relative mt-0">
            <h3 class="relative border-b border-gray-300/70 dark:border-gray-600/70 pb-2.5 text-2xl font-medium text-gray-900 dark:text-white mb-4 before:absolute before:-bottom-px before:left-0 before:h-px before:w-24 before:bg-red-600 dark:before:bg-red-500 before:content-[''] transition-colors duration-200">
                Categories
            </h3>
            <div class="categories-slider-container w-full overflow-hidden"> 
                <div class="swiper categories-slider w-full">
                    <div class="swiper-wrapper">
                        @foreach($categories ?? [] as $category)
                        <div class="swiper-slide  !w-auto">
                            <a href="{{ route('category.show', $category->slug) }}"
                                class="text-black dark:text-white py-3 px-10 my-2 mx-1 rounded-md text-sm font-medium shadow-sm hover:shadow-md hover:scale-105 hover:opacity-90 transition-all duration-300 ease-in-out whitespace-nowrap inline-block">
                                {{ $category->name }}
                            </a>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endif
<section class="lg:py-8 md:py-4 py-2 bg-white dark:bg-gray-800 transition-colors duration-200">
    <div class="px-4  mx-auto container sm:px-6 lg:px-8 md:flex lg:flex xl:flex lg:items-start gap-8">
        <div class="md:w-7/12 lg:w-8/12 xl:w-8/12">
            <!-- Advertisement Banner -->
            <div class="mb-6 w-full space-y-8 bg-gray-50 dark:bg-gray-700 p-6 rounded-xl transition-colors duration-200">
                <div class="text-gray-600 dark:text-gray-300 text-center">ADS HERE</div>
            </div>

            <!-- Recent News Section -->
            <h3 class=" relative sticky  border-b border-gray-300/70 dark:border-gray-600/70 pb-2.5 text-2xl font-medium text-gray-900 dark:text-white before:absolute before:-bottom-px before:left-0 before:h-px before:w-24 before:bg-red-600 dark:before:bg-red-500 before:content-[''] mb-5 transition-colors duration-200">
                Recent News
            </h3>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-3 md:gap-5" id="recent-news-container">
                @forelse($recentNews as $news)
                <article class="py-0 flex">
                    <a class="article-image w-2/5 flex-shrink-0" href="{{ route('news.show', $news->slug) }}">
                        <div class="group aspect-h-9 aspect-w-16 overflow-hidden rounded bg-gray-100 dark:bg-gray-800 relative">
                            <img alt="{{ $news->title }}"
                                loading="lazy"
                                decoding="async"
                                class="rounded object-cover object-center transition duration-300 ease-in-out group-hover:scale-110"
                                style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent"
                                src="{{ asset('storage/' . $news->main_image) }}"
                                onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <!-- Image placeholder -->
                            <div class="absolute inset-0 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center" style="display:none;">
                                <svg class="h-8 w-8 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                            </div>
                        </div>
                    </a>
                    <div class="ml-3 flex-1">
                        @if($news->categories->isNotEmpty())
                        <a class="text-xs font-medium uppercase tracking-widest transition-colors duration-300 hover:opacity-90"
                            href="{{ route('category.show', $news->categories->first()->slug) }}"
                            style="color: <?= $news->categories->first()->color ?>;">
                            {{ $news->categories->first()->name }}
                        </a>
                        @endif
                        <a href="{{ route('news.show', $news->slug) }}">
                            <h3 class="text-sm lg:text-sm font-medium leading-normal tracking-normal text-gray-900 dark:text-white decoration-gray-800 dark:decoration-gray-200 decoration-2 transition duration-300 ease-in-out">
                                {{ $news->title }}
                            </h3>
                        </a>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                            {{ $news->published_at->format('M d, Y') }}
                        </p>
                    </div>
                </article>
                @empty
                <div class="col-span-2 text-center py-6">
                    <p class="text-gray-500 dark:text-gray-400">No recent news available</p>
                </div>
                @endforelse
            </div>

            <div id="loading-indicator" class="text-center py-6 hidden">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-red-600"></div>
            </div>

            <div id="load-more-container" class="text-center py-6">
                <button id="load-more-btn" class="px-6 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors duration-200">
                    Load More
                </button>
            </div>

            <div id="end-of-content" class="text-center py-6 hidden">
                <p class="text-gray-500 dark:text-gray-400">No more news to load</p>
            </div>

             <!-- Recent News Section -->
           <!-- <h3 class=" relative sticky  border-b border-gray-300/70 dark:border-gray-600/70 pb-2.5 text-2xl font-medium text-gray-900 dark:text-white before:absolute before:-bottom-px before:left-0 before:h-px before:w-24 before:bg-red-600 dark:before:bg-red-500 before:content-[''] mb-5 transition-colors duration-200">
                India
            </h3>
             <div class="grid grid-cols-1 lg:grid-cols-2 gap-3 md:gap-5">
                @forelse($indiaNews as $news)
                <article class="py-0 flex">
                    <a class="article-image w-2/5 flex-shrink-0" href="{{ route('news.show', $news->slug) }}">
                        <div class="group aspect-h-9 aspect-w-16 overflow-hidden rounded bg-gray-100 dark:bg-gray-800 relative">
                            <img alt="{{ $news->title }}"
                                loading="lazy"
                                decoding="async"
                                class="rounded object-cover object-center transition duration-300 ease-in-out group-hover:scale-110"
                                style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent"
                                src="{{ asset('storage/' . $news->main_image) }}"
                                onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="absolute inset-0 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center" style="display:none;">
                                <svg class="h-8 w-8 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                            </div>
                        </div>
                    </a>
                    <div class="ml-3 flex-1">
                        @if($news->categories->isNotEmpty())
                        <a class="text-xs font-medium uppercase tracking-widest transition-colors duration-300 hover:opacity-90"
                            href="{{ route('category.show', $news->categories->first()->slug) }}"
                            style="color: <?= $news->categories->first()->color ?>;">
                            {{ $news->categories->first()->name }}
                        </a>
                        @endif
                        <a href="{{ route('news.show', $news->slug) }}">
                            <h3 class="text-sm lg:text-sm font-medium leading-normal tracking-normal text-gray-900 dark:text-white decoration-gray-800 dark:decoration-gray-200 decoration-2 transition duration-300 ease-in-out">
                                {{ $news->title }}
                            </h3>
                        </a>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                            {{ $news->published_at->format('M d, Y') }}
                        </p>
                    </div>
                </article>
                @empty
                <div class="col-span-2 text-center py-6">
                    <p class="text-gray-500 dark:text-gray-400">No india news available</p>
                </div>
                @endforelse
            </div>-->
        </div>
        <div class="md:w-5/12 lg:w-4/12 xl:w-4/12 sm:mt-8 md:mt-0">
            <!-- Featured Section -->
            <!-- <div class=" bg-gray-50 dark:bg-gray-700 rounded-xl p-6 ">
                <h2 class="relative border-b border-gray-300/70 pb-2.5 text-2xl font-medium text-gray-900 dark:text-white mb-8 before:absolute before:-bottom-px before:left-0 before:h-px before:w-24 before:bg-red-600 before:content-['']">
                    Featured
                </h2>
                <div class="space-y-6 sm:space-y-5 lg:space-y-6 xl:space-y-5">
                    <article class="flex space-x-4 sm:space-x-6 lg:space-x-4">
                        <a class="group relative z-10 h-24 w-24 overflow-hidden rounded-2xl bg-gray-100 sm:h-28 sm:w-28 lg:h-20 lg:w-20 xl:h-24 xl:w-24" href="#">
                            <img alt="The 7 Best Monitors in the Market"
                                loading="lazy"
                                decoding="async"
                                class="h-full w-full rounded-2xl object-cover object-center transition duration-300 ease-in-out group-hover:scale-110"
                                style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent"
                                src="https://images.unsplash.com/photo-1527443224154-c4a3942d3acf?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                                onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="absolute inset-0 bg-gray-200 rounded-2xl flex items-center justify-center" style="display:none;">
                                <svg class="h-8 w-8 text-gray-400 dark:text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                            </div>
                        </a>
                        <div class="w-2/3">
                            <div class="flex h-full w-full flex-1 flex-col justify-center">
                                <div>
                                    <a class="text-md font-medium leading-snug tracking-normal text-gray-900 dark:text-white decoration-gray-800 decoration-2 transition duration-300 ease-in-out " href="#">
                                        The 7 Best Monitors in the Market
                                    </a>
                                </div>
                                <div class="mt-2 flex items-center justify-between">
                                    <div class="flex items-center justify-center">
                                        <div class="text-sm">
                                            <span class="text-gray-500 dark:text-gray-400">By </span>
                                            <a class="font-medium text-gray-700 " href="#">Mark Jack</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </article>
                </div>
            </div> -->

            <!-- Popular Tags Section -->
            <div class="rounded-xl bg-gray-50 dark:bg-gray-700 p-5 sm:p-8 ">
                <h3 class="relative border-b border-gray-300/70 pb-2.5 text-2xl font-medium text-gray-900 dark:text-white before:absolute before:-bottom-px before:left-0 before:h-px before:w-24 before:bg-red-600 before:content-['']">
                    Popular tags
                </h3>
                <div class="pt-5">
                    <ul class="flex flex-wrap justify-start gap-2">
                        <li>
                            <a href="/tags/work">
                                <span class="inline-flex items-center rounded-full border border-gray-300/70 dark:border-gray-600/70 bg-transparent px-4 py-1 text-sm font-medium text-gray-800 dark:text-gray-200 transition duration-300 ease-in-out hover:text-red-700 dark:hover:text-red-400 sm:px-4 sm:py-1.5">
                                    Work
                                </span>
                            </a>
                        </li>
                        <li>
                            <a href="/tags/tips">
                                <span class="inline-flex items-center rounded-full border border-gray-300/70 dark:border-gray-600/70 bg-transparent px-4 py-1 text-sm font-medium text-gray-800 dark:text-gray-200 transition duration-300 ease-in-out hover:text-red-700 dark:hover:text-red-400 sm:px-4 sm:py-1.5">
                                    Tips
                                </span>
                            </a>
                        </li>
                        <li>
                            <a href="/tags/business">
                                <span class="inline-flex items-center rounded-full border border-gray-300/70 dark:border-gray-600/70 bg-transparent px-4 py-1 text-sm font-medium text-gray-800 dark:text-gray-200 transition duration-300 ease-in-out hover:text-red-700 dark:hover:text-red-400 sm:px-4 sm:py-1.5">
                                    Business
                                </span>
                            </a>
                        </li>
                        <li>
                            <a href="/tags/reviews">
                                <span class="inline-flex items-center rounded-full border border-gray-300/70 dark:border-gray-600/70 bg-transparent px-4 py-1 text-sm font-medium text-gray-800 dark:text-gray-200 transition duration-300 ease-in-out hover:text-red-700 dark:hover:text-red-400 sm:px-4 sm:py-1.5">
                                    Reviews
                                </span>
                            </a>
                        </li>
                        <li>
                            <a href="/tags/growth">
                                <span class="inline-flex items-center rounded-full border border-gray-300/70 dark:border-gray-600/70 bg-transparent px-4 py-1 text-sm font-medium text-gray-800 dark:text-gray-200 transition duration-300 ease-in-out hover:text-red-700 dark:hover:text-red-400 sm:px-4 sm:py-1.5">
                                    Growth
                                </span>
                            </a>
                        </li>
                        <li>
                            <a href="/tags/deeper-look">
                                <span class="inline-flex items-center rounded-full border border-gray-300/70 dark:border-gray-600/70 bg-transparent px-4 py-1 text-sm font-medium text-gray-800 dark:text-gray-200 transition duration-300 ease-in-out hover:text-red-700 dark:hover:text-red-400 sm:px-4 sm:py-1.5">
                                    Deeper Look
                                </span>
                            </a>
                        </li>
                        <li>
                            <a href="/tags/gaming">
                                <span class="inline-flex items-center rounded-full border border-gray-300/70 dark:border-gray-600/70 bg-transparent px-4 py-1 text-sm font-medium text-gray-800 dark:text-gray-200 transition duration-300 ease-in-out hover:text-red-700 dark:hover:text-red-400 sm:px-4 sm:py-1.5">
                                    Gaming
                                </span>
                            </a>
                        </li>
                        <li>
                            <a href="/tags/streaming">
                                <span class="inline-flex items-center rounded-full border border-gray-300/70 dark:border-gray-600/70 bg-transparent px-4 py-1 text-sm font-medium text-gray-800 dark:text-gray-200 transition duration-300 ease-in-out hover:text-red-700 dark:hover:text-red-400 sm:px-4 sm:py-1.5">
                                    Streaming
                                </span>
                            </a>
                        </li>
                        <li>
                            <a href="/tags/idea">
                                <span class="inline-flex items-center rounded-full border border-gray-300/70 dark:border-gray-600/70 bg-transparent px-4 py-1 text-sm font-medium text-gray-800 dark:text-gray-200 transition duration-300 ease-in-out hover:text-red-700 dark:hover:text-red-400 sm:px-4 sm:py-1.5">
                                    Idea
                                </span>
                            </a>
                        </li>
                        <li>
                            <a href="/tags/environment">
                                <span class="inline-flex items-center rounded-full border border-gray-300/70 dark:border-gray-600/70 bg-transparent px-4 py-1 text-sm font-medium text-gray-800 dark:text-gray-200 transition duration-300 ease-in-out hover:text-red-700 dark:hover:text-red-400 sm:px-4 sm:py-1.5">
                                    Environment
                                </span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Follow Us Section -->
            <div class="w-full rounded-2xl bg-gray-50 dark:bg-gray-700 p-5 sm:p-8 mt-8">
                <h3 class="relative border-b border-gray-300/70 pb-2.5 text-2xl font-medium text-gray-900 dark:text-white before:absolute before:-bottom-px before:left-0 before:h-px before:w-24 before:bg-red-600 before:content-['']">
                    Follow us
                </h3>
                <div class="pt-5">
                    <div class="overflow-hidden">
                        <!-- Facebook -->
                        <a class="group flex w-full items-center justify-between" href="#">
                            <div class="flex items-center">
                                <div>
                                    <span class="flex items-center justify-center w-9 h-9 duration-300 ease-in-out bg-transparent border rounded-full border-gray-300/70 dark:border-gray-600/70 group hover:bg-gray-50 dark:hover:bg-gray-600">
                                        <span class="sr-only">facebook</span>
                                        <svg height="24" width="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" fill="currentColor" class="w-3.5 h-3.5 text-gray-700 dark:text-gray-300 duration-300 ease-in-out group-hover:text-red-700 dark:group-hover:text-red-400">
                                            <g>
                                                <path d="M24,12.072A12,12,0,1,0,10.125,23.926V15.541H7.078V12.072h3.047V9.428c0-3.007,1.792-4.669,4.532-4.669a18.611,18.611,0,0,1,2.687.234V7.947H15.83a1.734,1.734,0,0,0-1.947,1.49,1.71,1.71,0,0,0-.008.385v2.25H17.2l-.532,3.469h-2.8v8.385A12,12,0,0,0,24,12.072Z"></path>
                                            </g>
                                        </svg>
                                    </span>
                                </div>
                                <div class="relative col-span-3 flex flex-col flex-wrap">
                                    <div class="box-border flex w-full flex-1 flex-col justify-between px-6 md:px-0">
                                        <div class="relative z-10 ml-3 text-base font-medium capitalize text-gray-700 dark:text-gray-300 transition-colors duration-300 ease-in-out group-hover:text-red-600 dark:group-hover:text-red-400">
                                            facebook
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" class="ml-2 h-5 w-5 text-red-400 transition duration-300 ease-in-out group-hover:translate-x-1.5 group-hover:text-red-600 dark:group-hover:text-red-400">
                                    <path fill-rule="evenodd" d="M5 10a.75.75 0 01.75-.75h6.638L10.23 7.29a.75.75 0 111.04-1.08l3.5 3.25a.75.75 0 010 1.08l-3.5 3.25a.75.75 0 11-1.04-1.08l2.158-1.96H5.75A.75.75 0 015 10z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        </a>

                        <hr class="my-2.5 ml-13 w-full border-t border-dashed border-gray-300/70 dark:border-gray-600/70" />

                        <!-- Instagram -->
                        <a class="group flex w-full items-center justify-between" href="#">
                            <div class="flex items-center">
                                <div>
                                    <span class="flex items-center justify-center w-9 h-9 duration-300 ease-in-out bg-transparent border rounded-full border-gray-300/70 dark:border-gray-600/70 group hover:bg-gray-50 dark:hover:bg-gray-600">
                                        <span class="sr-only">instagram</span>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" class="w-3.5 h-3.5 text-gray-700 dark:text-gray-300 duration-300 ease-in-out group-hover:text-red-700 dark:group-hover:text-red-400">
                                            <g>
                                                <path d="M12,2.982c2.937,0,3.285.011,4.445.064a6.072,6.072,0,0,1,2.042.379,3.4,3.4,0,0,1,1.265.823,3.4,3.4,0,0,1,.823,1.265,6.072,6.072,0,0,1,.379,2.042c.053,1.16.064,1.508.064,4.445s-.011,3.285-.064,4.445a6.072,6.072,0,0,1-.379,2.042,3.644,3.644,0,0,1-2.088,2.088,6.072,6.072,0,0,1-2.042.379c-1.16.053-1.508.064-4.445.064s-3.285-.011-4.445-.064a6.072,6.072,0,0,1-2.042-.379,3.4,3.4,0,0,1-1.265-.823,3.4,3.4,0,0,1-.823-1.265,6.072,6.072,0,0,1-.379-2.042c-.053-1.16-.064-1.508-.064-4.445s.011-3.285.064-4.445a6.072,6.072,0,0,1,.379-2.042,3.4,3.4,0,0,1,.823-1.265,3.4,3.4,0,0,1,1.265-.823,6.072,6.072,0,0,1,2.042-.379c1.16-.053,1.508-.064,4.445-.064M12,1c-2.987,0-3.362.013-4.535.066a8.108,8.108,0,0,0-2.67.511A5.625,5.625,0,0,0,1.577,4.8a8.108,8.108,0,0,0-.511,2.67C1.013,8.638,1,9.013,1,12s.013,3.362.066,4.535a8.108,8.108,0,0,0,.511,2.67A5.625,5.625,0,0,0,4.8,22.423a8.108,8.108,0,0,0,2.67.511C8.638,22.987,9.013,23,12,23s3.362-.013,4.535-.066a8.108,8.108,0,0,0,2.67-.511A5.625,5.625,0,0,0,22.423,19.2a8.108,8.108,0,0,0,.511-2.67C22.987,15.362,23,14.987,23,12s-.013-3.362-.066-4.535a8.108,8.108,0,0,0-.511-2.67A5.625,5.625,0,0,0,19.2,1.577a8.108,8.108,0,0,0-2.67-.511C15.362,1.013,14.987,1,12,1Z"></path>
                                                <path d="M12,6.351A5.649,5.649,0,1,0,17.649,12,5.649,5.649,0,0,0,12,6.351Zm0,9.316A3.667,3.667,0,1,1,15.667,12,3.667,3.667,0,0,1,12,15.667Z"></path>
                                                <circle cx="17.872" cy="6.128" r="1.32"></circle>
                                            </g>
                                        </svg>
                                    </span>
                                </div>
                                <div class="relative col-span-3 flex flex-col flex-wrap">
                                    <div class="box-border flex w-full flex-1 flex-col justify-between px-6 md:px-0">
                                        <div class="relative z-10 ml-3 text-base font-medium capitalize text-gray-700 dark:text-gray-300 transition-colors duration-300 ease-in-out group-hover:text-red-600 dark:group-hover:text-red-400">
                                            instagram
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" class="ml-2 h-5 w-5 text-red-400 transition duration-300 ease-in-out group-hover:translate-x-1.5 group-hover:text-red-600 dark:group-hover:text-red-400">
                                    <path fill-rule="evenodd" d="M5 10a.75.75 0 01.75-.75h6.638L10.23 7.29a.75.75 0 111.04-1.08l3.5 3.25a.75.75 0 010 1.08l-3.5 3.25a.75.75 0 11-1.04-1.08l2.158-1.96H5.75A.75.75 0 015 10z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        </a>

                        <hr class="my-2.5 ml-13 w-full border-t border-dashed border-gray-300/70 dark:border-gray-600/70" />

                        <!-- Twitter -->
                        <a class="group flex w-full items-center justify-between" href="#">
                            <div class="flex items-center">
                                <div>
                                    <span class="flex items-center justify-center w-9 h-9 duration-300 ease-in-out bg-transparent border rounded-full border-gray-300/70 dark:border-gray-600/70 group hover:bg-gray-50 dark:hover:bg-gray-600">
                                        <span class="sr-only">twitter</span>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" class="w-3.5 h-3.5 text-gray-700 dark:text-gray-300 duration-300 ease-in-out group-hover:text-red-700 dark:group-hover:text-red-400">
                                            <g>
                                                <path d="M24,4.6c-0.9,0.4-1.8,0.7-2.8,0.8c1-0.6,1.8-1.6,2.2-2.7c-1,0.6-2,1-3.1,1.2c-0.9-1-2.2-1.6-3.6-1.6 c-2.7,0-4.9,2.2-4.9,4.9c0,0.4,0,0.8,0.1,1.1C7.7,8.1,4.1,6.1,1.7,3.1C1.2,3.9,1,4.7,1,5.6c0,1.7,0.9,3.2,2.2,4.1 C2.4,9.7,1.6,9.5,1,9.1c0,0,0,0,0,0.1c0,2.4,1.7,4.4,3.9,4.8c-0.4,0.1-0.8,0.2-1.3,0.2c-0.3,0-0.6,0-0.9-0.1c0.6,2,2.4,3.4,4.6,3.4 c-1.7,1.3-3.8,2.1-6.1,2.1c-0.4,0-0.8,0-1.2-0.1c2.2,1.4,4.8,2.2,7.5,2.2c9.1,0,14-7.5,14-14c0-0.2,0-0.4,0-0.6 C22.5,6.4,23.3,5.5,24,4.6z"></path>
                                            </g>
                                        </svg>
                                    </span>
                                </div>
                                <div class="relative col-span-3 flex flex-col flex-wrap">
                                    <div class="box-border flex w-full flex-1 flex-col justify-between px-6 md:px-0">
                                        <div class="relative z-10 ml-3 text-base font-medium capitalize text-gray-700 dark:text-gray-300 transition-colors duration-300 ease-in-out group-hover:text-red-600 dark:group-hover:text-red-400">
                                            twitter
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" class="ml-2 h-5 w-5 text-red-400 transition duration-300 ease-in-out group-hover:translate-x-1.5 group-hover:text-red-600 dark:group-hover:text-red-400">
                                    <path fill-rule="evenodd" d="M5 10a.75.75 0 01.75-.75h6.638L10.23 7.29a.75.75 0 111.04-1.08l3.5 3.25a.75.75 0 010 1.08l-3.5 3.25a.75.75 0 11-1.04-1.08l2.158-1.96H5.75A.75.75 0 015 10z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        </a>

                        <hr class="my-2.5 ml-13 w-full border-t border-dashed border-gray-300/70 dark:border-gray-600/70" />

                        <!-- LinkedIn -->
                        <a class="group flex w-full items-center justify-between" href="#">
                            <div class="flex items-center">
                                <div>
                                    <span class="flex items-center justify-center w-9 h-9 duration-300 ease-in-out bg-transparent border rounded-full border-gray-300/70 dark:border-gray-600/70 group hover:bg-gray-50 dark:hover:bg-gray-600">
                                        <span class="sr-only">linkedin</span>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="16" viewBox="0 0 18 16" fill="currentColor" class="w-3.5 h-3.5 text-gray-700 dark:text-gray-300 duration-300 ease-in-out group-hover:text-red-700 dark:group-hover:text-red-400">
                                            <path d="M16.5128 0H0.717949C0.287179 0 0 0.266667 0 0.666667V15.3333C0 15.7333 0.287179 16 0.717949 16H16.5128C16.9436 16 17.2308 15.7333 17.2308 15.3333V0.666667C17.2308 0.266667 16.9436 0 16.5128 0ZM5.09744 13.6667H2.58462V6H5.16923V13.6667H5.09744ZM3.80513 4.93333C3.01538 4.93333 2.29744 4.33333 2.29744 3.53333C2.29744 2.8 2.94359 2.13333 3.80513 2.13333C4.59487 2.13333 5.31282 2.73333 5.31282 3.53333C5.31282 4.33333 4.66667 4.93333 3.80513 4.93333ZM14.7179 13.6667H12.1333V9.93333C12.1333 9.06667 12.1333 7.93333 10.841 7.93333C9.47692 7.93333 9.33333 8.86667 9.33333 9.86667V13.6667H6.74872V6H9.18974V7.06667C9.54872 6.46667 10.3385 5.86667 11.6308 5.86667C14.2154 5.86667 14.7179 7.46667 14.7179 9.53333V13.6667Z"></path>
                                        </svg>
                                    </span>
                                </div>
                                <div class="relative col-span-3 flex flex-col flex-wrap">
                                    <div class="box-border flex w-full flex-1 flex-col justify-between px-6 md:px-0">
                                        <div class="relative z-10 ml-3 text-base font-medium capitalize text-gray-700 dark:text-gray-300 transition-colors duration-300 ease-in-out group-hover:text-red-600 dark:group-hover:text-red-400">
                                            linkedin
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" class="ml-2 h-5 w-5 text-red-400 transition duration-300 ease-in-out group-hover:translate-x-1.5 group-hover:text-red-600 dark:group-hover:text-red-400">
                                    <path fill-rule="evenodd" d="M5 10a.75.75 0 01.75-.75h6.638L10.23 7.29a.75.75 0 111.04-1.08l3.5 3.25a.75.75 0 010 1.08l-3.5 3.25a.75.75 0 11-1.04-1.08l2.158-1.96H5.75A.75.75 0 015 10z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        </a>

                        <hr class="my-2.5 ml-13 w-full border-t border-dashed border-gray-300/70 dark:border-gray-600/70" />
                    </div>
                </div>
            </div>

            <!-- Advertisement Banner -->
            <div class="my-6 w-full space-y-8 bg-gray-50 dark:bg-gray-700 p-6 rounded-xl">
                ADS HERE
            </div>
        </div>
    </div>
</section>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        let offset = <?= count($recentNews) ?>;
        const limit = 10;
        let loading = false;
        let hasMore = true;

        const loadingIndicator = document.getElementById('loading-indicator');
        const loadMoreBtn = document.getElementById('load-more-btn');
        const loadMoreContainer = document.getElementById('load-more-container');
        const endOfContent = document.getElementById('end-of-content');
        const recentNewsContainer = document.getElementById('recent-news-container');
        
        // Hide load more button if no news exists
        if (offset === 0) {
            loadMoreContainer.classList.add('hidden');
        }

        function loadMoreNews() {
            if (loading || !hasMore) return;

            loading = true;
            loadingIndicator.classList.remove('hidden');
            loadMoreBtn.disabled = true;

            // Use fetch API with proper headers
            fetch('{{ route("home.load-more") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({
                        offset,
                        limit
                    })
                })
                .then(response => response.json())
                .then(data => {
                    loadingIndicator.classList.add('hidden');
                    loadMoreBtn.disabled = false;

                    if (data.html) {
                        recentNewsContainer.insertAdjacentHTML('beforeend', data.html);
                        offset += limit;
                    }

                    hasMore = data.hasMore;
                    if (!hasMore) {
                        loadMoreContainer.classList.add('hidden');
                        endOfContent.classList.remove('hidden');
                    }

                    loading = false;
                })
                .catch(error => {
                    console.error('Error loading more news:', error);
                    loadingIndicator.classList.add('hidden');
                    loadMoreBtn.disabled = false;
                    loading = false;
                });
        }

        // Add click event listener to the load more button
        loadMoreBtn.addEventListener('click', loadMoreNews);
    });
</script>
@endpush
@endsection