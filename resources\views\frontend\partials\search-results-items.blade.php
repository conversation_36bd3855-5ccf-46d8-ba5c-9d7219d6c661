@foreach($articles as $item)
<div class="flex flex-col md:flex-row items-start gap-4 py-4 border-b border-gray-200 dark:border-gray-700 transition-colors duration-200">
    <a href="{{ route('news.show', $item->slug) }}" class="w-full md:w-1/3 flex-shrink-0">
        <div class="aspect-w-16 aspect-h-9 bg-gray-100 dark:bg-gray-800 rounded overflow-hidden transition-colors duration-200">
            <img 
                src="{{ asset('storage/' . $item->main_image) }}" 
                alt="{{ $item->title }}" 
                class="w-full h-full object-cover transform transition duration-500 ease-in-out hover:scale-110"
                onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
            <!-- Image placeholder -->
            <div class="absolute inset-0 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center" style="display:none;">
                <svg class="h-8 w-8 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
            </div>
        </div>
    </a>
    <div class="ml-0 md:ml-3 flex-1">
        @if($item->categories->isNotEmpty())
            <a href="{{ route('category.show', $item->categories->first()->slug) }}" 
               class="text-xs font-medium uppercase tracking-widest transition-colors duration-300 ease-in-out"
               style="color: {{ $item->categories->first()->color }}">
                {{ $item->categories->first()->name }}
            </a>
        @else
            <span class="text-xs font-medium uppercase tracking-widest text-gray-500 dark:text-gray-400">
                Uncategorized
            </span>
        @endif
        <h2 class="mt-2 text-xl font-semibold text-gray-900 dark:text-white transition-colors duration-200">
            <a href="{{ route('news.show', $item->slug) }}" class="hover:text-red-600 dark:hover:text-red-500 transition-colors duration-300 ease-in-out">
                {{ $item->title }}
            </a>
        </h2>
        <div class="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400 transition-colors duration-200">
            <span class="inline-flex items-center">
                <svg class="mr-1.5 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                {{ Carbon\Carbon::parse($item->published_at)->format('M d, Y') }}
            </span>
        </div>
    </div>
</div>
@endforeach1