@extends('layouts.frontend')

@section('title', 'About Us')

@section('meta')
<meta name="description" content="Learn about our mission, values, and commitment to delivering quality news and information. Discover our story and meet our team.">
<meta name="keywords" content="about us, our mission, news organization, journalism, team, company story">
<meta name="robots" content="index, follow">
@endsection

@push('styles')
<style>
    .hero-gradient {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .stats-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .team-card {
        transition: all 0.3s ease;
    }

    .team-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    }

    .timeline-item {
        position: relative;
    }

    .timeline-item::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #e5e7eb;
    }

    .dark .timeline-item::before {
        background: #374151;
    }

    .timeline-item:last-child::before {
        display: none;
    }

    .timeline-dot {
        position: absolute;
        left: 11px;
        top: 8px;
        width: 10px;
        height: 10px;
        background: #3b82f6;
        border-radius: 50%;
        border: 2px solid white;
        box-shadow: 0 0 0 3px #e5e7eb;
    }

    .dark .timeline-dot {
        border-color: #1f2937;
        box-shadow: 0 0 0 3px #374151;
    }

    @media (max-width: 640px) {
        .about-content {
            font-size: 0.9rem;
            line-height: 1.6;
        }

        .about-content h2 {
            font-size: 1.5rem !important;
        }

        .about-content h3 {
            font-size: 1.25rem !important;
        }

        .stats-grid {
            grid-template-columns: repeat(2, 1fr) !important;
        }
    }

    /* Force text colors for better visibility */
    .about-content p {
        color: #374151 !important;
    }

    .dark .about-content p {
        color: #d1d5db !important;
    }

    .about-content strong {
        color: #111827 !important;
    }

    .dark .about-content strong {
        color: #ffffff !important;
    }

    .about-content li {
        color: #374151 !important;
    }

    .dark .about-content li {
        color: #d1d5db !important;
    }

    .about-content h2 {
        color: #111827 !important;
    }

    .dark .about-content h2 {
        color: #ffffff !important;
    }

    .about-content h3 {
        color: #111827 !important;
    }

    .dark .about-content h3 {
        color: #ffffff !important;
    }
</style>
@endpush

@section('content')
<!-- Hero Section -->
<div class="hero-gradient text-white py-16 md:py-24 mb-12">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto text-center">
            <h1 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">About {{ config('app.name') }}</h1>
            <p class="text-lg md:text-xl opacity-90 mb-8 leading-relaxed">
                Delivering trusted news and insightful analysis to keep you informed about the world around you.
            </p>
            <div class="flex flex-col sm:flex-row justify-center gap-4">
                <a href="#mission" class="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                    Our Mission
                </a>
                <a href="#team" class="border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                    Meet Our Team
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Stats Section -->
<div class="container mx-auto px-4 mb-16">
    <div class="stats-grid grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-8">
        <div class="stats-card bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 text-center">
            <div class="text-2xl md:text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">5+</div>
            <div class="text-sm md:text-base text-gray-600 dark:text-gray-300">Years of Excellence</div>
        </div>
        <div class="stats-card bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 text-center">
            <div class="text-2xl md:text-3xl font-bold text-green-600 dark:text-green-400 mb-2">10K+</div>
            <div class="text-sm md:text-base text-gray-600 dark:text-gray-300">Articles Published</div>
        </div>
        <div class="stats-card bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 text-center">
            <div class="text-2xl md:text-3xl font-bold text-purple-600 dark:text-purple-400 mb-2">50K+</div>
            <div class="text-sm md:text-base text-gray-600 dark:text-gray-300">Daily Readers</div>
        </div>
        <div class="stats-card bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 text-center">
            <div class="text-2xl md:text-3xl font-bold text-orange-600 dark:text-orange-400 mb-2">24/7</div>
            <div class="text-sm md:text-base text-gray-600 dark:text-gray-300">News Coverage</div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="container mx-auto px-4 pb-16">
    <div class="max-w-6xl mx-auto">
        <!-- Mission Section -->
        <section id="mission" class="mb-16">
            <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 md:p-8 lg:p-12">
                <div class="text-center mb-8">
                    <h2 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4">Our Mission</h2>
                    <p class="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                        To provide accurate, timely, and unbiased news coverage that empowers our readers to make informed decisions about their world.
                    </p>
                </div>

                <div class="grid md:grid-cols-3 gap-6 md:gap-8">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Accuracy</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-300">We verify facts and sources to ensure the highest standards of journalistic integrity.</p>
                    </div>

                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Speed</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-300">Breaking news delivered as it happens, keeping you ahead of the curve.</p>
                    </div>

                    <div class="text-center">
                        <div class="w-16 h-16 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Global Reach</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-300">Comprehensive coverage of local, national, and international news stories.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Story Section -->
        <section class="mb-16">
            <div class="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
                <div class="about-content">
                    <h2 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-6">Our Story</h2>
                    <p class="text-gray-600 dark:text-gray-300 mb-4 leading-relaxed">
                        Founded in {{ date('Y') - 5 }}, <strong>{{ config('app.name') }}</strong> began as a small team of passionate journalists committed to delivering quality news coverage. What started as a local news initiative has grown into a trusted source for readers worldwide.
                    </p>
                    <p class="text-gray-600 dark:text-gray-300 mb-4 leading-relaxed">
                        Our journey has been driven by a simple belief: that informed citizens make better decisions. We've built our reputation on thorough reporting, fact-checking, and presenting multiple perspectives on complex issues.
                    </p>
                    <p class="text-gray-600 dark:text-gray-300 leading-relaxed">
                        Today, we continue to evolve with the changing media landscape while maintaining our core commitment to journalistic excellence and public service.
                    </p>
                </div>

                <div class="bg-gray-100 dark:bg-gray-800 rounded-lg p-6 md:p-8">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">Our Timeline</h3>
                    <div class="space-y-6">
                        <div class="timeline-item pl-8">
                            <div class="timeline-dot"></div>
                            <div>
                                <div class="font-semibold text-gray-900 dark:text-white">{{ date('Y') - 5 }}</div>
                                <div class="text-sm text-gray-600 dark:text-gray-300">Founded with a team of 5 journalists</div>
                            </div>
                        </div>
                        <div class="timeline-item pl-8">
                            <div class="timeline-dot"></div>
                            <div>
                                <div class="font-semibold text-gray-900 dark:text-white">{{ date('Y') - 3 }}</div>
                                <div class="text-sm text-gray-600 dark:text-gray-300">Launched digital platform and mobile app</div>
                            </div>
                        </div>
                        <div class="timeline-item pl-8">
                            <div class="timeline-dot"></div>
                            <div>
                                <div class="font-semibold text-gray-900 dark:text-white">{{ date('Y') - 1 }}</div>
                                <div class="text-sm text-gray-600 dark:text-gray-300">Reached 50,000 daily readers milestone</div>
                            </div>
                        </div>
                        <div class="timeline-item pl-8">
                            <div class="timeline-dot"></div>
                            <div>
                                <div class="font-semibold text-gray-900 dark:text-white">{{ date('Y') }}</div>
                                <div class="text-sm text-gray-600 dark:text-gray-300">Expanding international coverage</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Values Section -->
        <section class="mb-16">
            <div class="text-center mb-12">
                <h2 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4">Our Values</h2>
                <p class="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                    These core principles guide everything we do, from story selection to publication.
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 text-center">
                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Integrity</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-300">Honest, transparent reporting without bias or agenda.</p>
                </div>

                <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 text-center">
                    <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Accountability</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-300">Taking responsibility for our reporting and corrections when needed.</p>
                </div>

                <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 text-center">
                    <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Diversity</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-300">Representing multiple perspectives and voices in our coverage.</p>
                </div>

                <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 text-center">
                    <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="w-6 h-6 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Innovation</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-300">Embracing new technologies to enhance our storytelling.</p>
                </div>
            </div>
        </section>

        <!-- Team Section -->
        <section id="team" class="mb-16">
            <div class="text-center mb-12">
                <h2 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4">Meet Our Team</h2>
                <p class="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                    Our dedicated team of journalists, editors, and content creators work around the clock to bring you the latest news.
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Team Member 1 -->
                <div class="team-card bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 text-center">
                    <div class="w-24 h-24 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl font-bold text-white">JD</span>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-1">John Doe</h3>
                    <p class="text-blue-600 dark:text-blue-400 text-sm font-medium mb-3">Editor-in-Chief</p>
                    <p class="text-sm text-gray-600 dark:text-gray-300 mb-4">
                        With over 15 years in journalism, John leads our editorial team with a focus on investigative reporting and fact-checking.
                    </p>
                    <div class="flex justify-center space-x-3">
                        <a href="#" class="text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Team Member 2 -->
                <div class="team-card bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 text-center">
                    <div class="w-24 h-24 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl font-bold text-white">SM</span>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-1">Sarah Miller</h3>
                    <p class="text-green-600 dark:text-green-400 text-sm font-medium mb-3">Senior Reporter</p>
                    <p class="text-sm text-gray-600 dark:text-gray-300 mb-4">
                        Sarah specializes in political coverage and international affairs, bringing complex stories to life with clear, engaging reporting.
                    </p>
                    <div class="flex justify-center space-x-3">
                        <a href="#" class="text-gray-400 hover:text-green-600 dark:hover:text-green-400 transition-colors">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-green-600 dark:hover:text-green-400 transition-colors">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Team Member 3 -->
                <div class="team-card bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 text-center">
                    <div class="w-24 h-24 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl font-bold text-white">MJ</span>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-1">Michael Johnson</h3>
                    <p class="text-purple-600 dark:text-purple-400 text-sm font-medium mb-3">Tech Editor</p>
                    <p class="text-sm text-gray-600 dark:text-gray-300 mb-4">
                        Michael covers technology trends, cybersecurity, and digital innovation, making complex tech topics accessible to all readers.
                    </p>
                    <div class="flex justify-center space-x-3">
                        <a href="#" class="text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 transition-colors">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 transition-colors">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact CTA Section -->
        <section class="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-8 md:p-12 text-center">
            <h2 class="text-2xl md:text-3xl font-bold mb-4">Get in Touch</h2>
            <p class="text-lg opacity-90 mb-8 max-w-2xl mx-auto">
                Have a story tip, feedback, or want to collaborate? We'd love to hear from you.
            </p>
            <div class="flex flex-col sm:flex-row justify-center gap-4">
                <a href="" class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                    Contact Us
                </a>
                <a href="#" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                    Newsletter
                </a>
            </div>
        </section>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add animation on scroll for stats cards
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe stats cards
    document.querySelectorAll('.stats-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });

    // Observe team cards
    document.querySelectorAll('.team-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
});
</script>
@endpush

@endsection