<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class NewsApiService
{
    protected $apiUrl;
    protected $apiKey;
    protected $timeout;

    public function __construct()
    {
        $this->apiUrl = config('services.news_api.url');
        $this->apiKey = config('services.news_api.key');
        $this->timeout = config('services.news_api.timeout', 30);
    }

    /**
     * Fetch latest news from external API
     *
     * @return array
     * @throws Exception
     */
    public function fetchLatestNews(array $params = [])
    {
        try {
            // Build query parameters
            $queryParams = [];
            
            // Add parameters if they exist
            if (isset($params['limit'])) $queryParams['pageSize'] = $params['limit'];
            if (isset($params['country'])) $queryParams['country'] = $params['country'];
            if (isset($params['language'])) $queryParams['language'] = $params['language'];
            if (isset($params['category'])) $queryParams['category'] = $params['category'];
            
            // Add default parameters if needed
            if (!isset($queryParams['pageSize'])) $queryParams['pageSize'] = 10;
            if (!isset($queryParams['country'])) $queryParams['country'] = 'us'; // Default country
            if (!isset($queryParams['language'])) $queryParams['language'] = 'en'; // Default language
            
            // Make API request with parameters
            $response = $this->client->get('top-headlines', [
                'query' => $queryParams
            ]);
            
            // Process response
            $data = json_decode($response->getBody()->getContents(), true);
            
            // Transform and return articles
            return $this->transformArticles($data['articles'] ?? []);
        } catch (\Exception $e) {
            Log::error('Error fetching news from API', [
                'error' => $e->getMessage(),
                'params' => $params
            ]);
            
            return [];
        }
    }

    /**
     * Process and normalize articles from API response
     *
     * @param array $articles
     * @return array
     */
    protected function processArticles(array $articles): array
    {
        $processedArticles = [];

        foreach ($articles as $article) {
            $processed = $this->normalizeArticle($article);
            
            if ($this->isValidArticle($processed)) {
                $processedArticles[] = $processed;
            }
        }

        return $processedArticles;
    }

    /**
     * Normalize article data to consistent format
     *
     * @param array $article
     * @return array
     */
    protected function normalizeArticle(array $article): array
    {
        return [
            'title' => $this->extractTitle($article),
            'category' => 'General', // Default category since API doesn't provide it
            'published_by' => 'News Source', // Default publisher
            'description' => null, // Not provided by API
            'source_url' => null, // Not provided by API
            'source_published_at' => $this->extractPublishedDate($article),
            'api_response' => $article, // Store original response
        ];
    }

    /**
     * Extract title from article data
     */
    protected function extractTitle(array $article): ?string
    {
        return $article['title'] ?? $article['headline'] ?? $article['name'] ?? null;
    }





    /**
     * Extract published date from article data
     */
    protected function extractPublishedDate(array $article): ?string
    {
        $dateField = $article['published_at'] ??
                    $article['publishedAt'] ??
                    $article['published'] ??
                    $article['date'] ??
                    $article['created_at'] ??
                    null;

        if ($dateField) {
            try {
                return date('Y-m-d H:i:s', strtotime($dateField));
            } catch (Exception $e) {
                Log::warning('Failed to parse date', ['date' => $dateField, 'error' => $e->getMessage()]);
            }
        }

        return null;
    }



    /**
     * Validate if article has required fields
     */
    protected function isValidArticle(array $article): bool
    {
        return !empty($article['title']);
    }

    /**
     * Handle different API response formats
     */
    protected function normalizeApiResponse(array $data): array
    {
        // If data is directly an array of articles
        if (isset($data[0]) && is_array($data[0])) {
            return $data;
        }

        // Handle your specific API format with top_stories
        if (isset($data['top_stories']) && is_array($data['top_stories'])) {
            return $data['top_stories'];
        }

        // If data has a different structure, try to extract articles
        foreach (['items', 'results', 'posts', 'news', 'articles'] as $key) {
            if (isset($data[$key]) && is_array($data[$key])) {
                return $data[$key];
            }
        }

        return [];
    }


}

