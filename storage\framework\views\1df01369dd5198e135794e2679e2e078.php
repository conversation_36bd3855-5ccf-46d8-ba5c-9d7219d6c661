<?php $__env->startSection('title', 'News Integration'); ?>

<?php
    $pageTitle = 'News Integration Management';
    $pageDescription = 'Manage external news sources and AI-generated content';
    $breadcrumbs = [
        ['title' => 'Dashboard', 'url' => route('admin.dashboard')],
        ['title' => 'News Integration', 'url' => '#']
    ];
?>

<?php $__env->startSection('page-header'); ?>
<div class="flex items-center justify-between">
    <div>
        <h1 class="text-3xl font-semibold text-gray-900"><?php echo e($pageTitle); ?></h1>
        <p class="mt-2 text-gray-600"><?php echo e($pageDescription); ?></p>
    </div>
    <div class="mt-4 sm:mt-0 flex space-x-3">
        <button id="fetch-news-btn" class="material-button material-button-md material-button-secondary flex items-center">
            <i class="material-icons mr-2">download</i>
            Fetch Latest News
        </button>
        <button id="generate-news-btn" class="material-button material-button-md material-button-primary flex items-center">
            <i class="material-icons mr-2">auto_awesome</i>
            Generate Content
        </button>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<!-- Fetch News Modal -->
<div id="fetch-news-modal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" id="fetch-modal-backdrop"></div>
        <div class="relative bg-white rounded-lg max-w-md w-full mx-auto shadow-xl">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Fetch Latest News</h3>
            </div>
            <div class="p-6">
                <form id="fetch-news-form">
                    <!-- Limit -->
                    <div class="mb-4">
                        <label for="news-limit" class="block text-sm font-medium text-gray-700 mb-1">Limit</label>
                        <input type="number" id="news-limit" name="limit" min="1" max="100" value="10" 
                               class="material-input w-full">
                    </div>
                    
                    <!-- Country -->
                    <div class="mb-4">
                        <label for="news-country" class="block text-sm font-medium text-gray-700 mb-1">Country</label>
                        <input type="text" id="news-country" name="country" placeholder="e.g. IN" 
                               class="material-input w-full">
                    </div>
                    
                    <!-- Language -->
                    <div class="mb-4">
                        <label for="news-language" class="block text-sm font-medium text-gray-700 mb-1">Language</label>
                        <input type="text" id="news-language" name="language" placeholder="e.g. en" 
                               class="material-input w-full">
                    </div>
                    
                    <!-- Category -->
                    <div class="mb-4">
                        <label for="news-category" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                        <select id="news-category" name="category" class="material-input w-full">
                            <option value="">All Categories</option>
                            <option value="business">Business</option>
                            <option value="entertainment">Entertainment</option>
                            <option value="general">General</option>
                            <option value="health">Health</option>
                            <option value="science">Science</option>
                            <option value="sports">Sports</option>
                            <option value="technology">Technology</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
                <button id="fetch-modal-cancel" class="material-button material-button-text">
                    Cancel
                </button>
                <button id="fetch-modal-submit" class="material-button material-button-primary">
                    Fetch News
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Titles -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="material-icons text-blue-600 text-lg">article</i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total Titles</p>
                <p class="text-2xl font-semibold text-gray-900" id="stat-total"><?php echo e($stats['total']); ?></p>
            </div>
        </div>
    </div>

    <!-- Processed -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <i class="material-icons text-yellow-600 text-lg">pending</i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Processed</p>
                <p class="text-2xl font-semibold text-gray-900" id="stat-processed"><?php echo e($stats['processed']); ?></p>
            </div>
        </div>
    </div>

    <!-- Generated -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="material-icons text-green-600 text-lg">check_circle</i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Generated</p>
                <p class="text-2xl font-semibold text-gray-900" id="stat-generated"><?php echo e($stats['generated']); ?></p>
            </div>
        </div>
    </div>

    <!-- Pending -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                    <i class="material-icons text-red-600 text-lg">schedule</i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Pending</p>
                <p class="text-2xl font-semibold text-gray-900" id="stat-pending"><?php echo e($stats['pending']); ?></p>
            </div>
        </div>
    </div>
</div>

<!-- News Integration DataTable -->
<div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
    <!-- DataTable Container -->
    <div class="p-6">
        <!-- Custom Controls -->
        <div class="mb-6 flex justify-end">
            <div class="flex items-center space-x-3">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="material-icons text-gray-400 text-sm">search</i>
                    </div>
                    <input type="text" id="search-input" placeholder="Search news titles..."
                           class="material-input pl-10 w-64">
                </div>
                <button id="refresh-table" class="material-button material-button-sm material-button-secondary p-2" title="Refresh">
                    <i class="material-icons text-sm">refresh</i>
                </button>
            </div>
        </div>

        <!-- Table -->
        <div>
            <table id="news-integration-table" class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Published Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!-- DataTables will populate this -->
                </tbody>
            </table>
        </div>
        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between">
                <div class="flex items-center space-x-2 mb-4 sm:mb-0">
                    <span class="text-sm text-gray-700">Show</span>
                    <select id="page-length" class="w-16">
                        <option value="10">10</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                    <span class="text-sm text-gray-700">entries</span>
                </div>
                <div id="table-info" class="text-sm text-gray-700 mb-4 sm:mb-0"></div>
                <div id="news-integration-pagination" class="w-full sm:w-auto"></div>
            </div>
        </div>
    </div>
</div>

<!-- Generate Content Modal -->
<div id="generate-content-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Generate AI Content</h3>
                <button id="close-generate-modal" class="text-gray-400 hover:text-gray-600">
                    <i class="material-icons">close</i>
                </button>
            </div>
            <div class="mb-4">
                <label for="generate-limit" class="block text-sm font-medium text-gray-700 mb-2">Number of articles to generate</label>
                <input type="number" id="generate-limit" value="5" min="1" max="20" class="material-input w-full">
                <p class="text-xs text-gray-500 mt-1">Uses Gemini AI (Free tier: 500 requests/day)</p>
            </div>
            <div class="flex justify-end space-x-3">
                <button id="cancel-generate" class="material-button material-button-sm material-button-secondary">Cancel</button>
                <button id="confirm-generate" class="material-button material-button-sm material-button-primary">Generate Content</button>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.tailwindcss.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
<style>
    .action-btn {
        @apply inline-flex items-center justify-center w-8 h-8 rounded-lg border border-gray-300 bg-white text-gray-600 hover:bg-gray-50 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200;
    }
    .action-btn.generate {
        @apply border-blue-300 text-blue-600 hover:bg-blue-50 hover:text-blue-700;
    }
    .action-btn.view {
        @apply border-green-300 text-green-600 hover:bg-green-50 hover:text-green-700;
    }
    .action-btn:disabled {
        @apply opacity-50 cursor-not-allowed;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- DataTables JS -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.tailwindcss.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable
    let table = $('#news-integration-table').DataTable({
        processing: true,
        serverSide: true,
        responsive: true,
        ajax: {
            url: '<?php echo e(route("admin.news-integration.data")); ?>',
            type: 'GET'
        },
        columns: [
            {
                data: 'title',
                name: 'title',
                orderable: true,
                searchable: true,
                render: function(data, type, row) {
                    return '<div class="font-medium text-gray-900 truncate max-w-xs" title="' + data + '">' + data + '</div>';
                }
            },
            {
                data: 'source_published_at',
                name: 'source_published_at',
                orderable: true,
                searchable: false,
                render: function(data) {
                    if (data) {
                        const date = new Date(data);
                        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
                    }
                    return '-';
                }
            },
            {
                data: 'status',
                name: 'status',
                orderable: false,
                searchable: false
            },
            {
                data: 'created_at',
                name: 'created_at',
                orderable: true,
                searchable: false
            },
            {
                data: 'actions',
                name: 'actions',
                orderable: false,
                searchable: false,
                className: 'text-right'
            }
        ],
        order: [[3, 'desc']], // Sort by created_at desc by default
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        language: {
            processing: '<div>Loading data...</div>',
            paginate: {
                previous: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-chevron-left"><polyline points="15 18 9 12 15 6"></polyline></svg>',
                next: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-chevron-right"><polyline points="9 18 15 12 9 6"></polyline></svg>'
            },
            info: "Showing _START_ to _END_ of _TOTAL_ entries"
        },
        dom: 'rtip', // Show table, processing, info, and pagination
        responsive: true,
        preDrawCallback: function() {
            // Add processing class to wrapper when processing
            if ($('.dataTables_processing').css('display') !== 'none') {
                $('.dataTables_wrapper').addClass('processing');
            }
        },
        drawCallback: function(settings) {
            // Remove processing class from wrapper when done
            $('.dataTables_wrapper').removeClass('processing');

            // Move pagination to custom container while preserving events
            const $pagination = $('.dataTables_paginate').detach();
            $('#news-integration-pagination').empty().append($pagination);

            // Move info to custom container
            const $info = $('.dataTables_info').detach();
            $('#table-info').empty().append($info);

            // Set the current page length in the dropdown
            $('#page-length').val(table.page.len());

            // Add custom class to pagination buttons
            $('.paginate_button').addClass('custom-paginate-button');

            // Ensure SVG icons are properly centered
            $('.paginate_button svg').parent().css({
                'display': 'flex',
                'align-items': 'center',
                'justify-content': 'center'
            });
        }
    });

    // Custom search functionality
    $('#search-input').on('keyup', function() {
        table.search($(this).val()).draw();
    });

    // Custom page length functionality
    $('#page-length').on('change', function() {
        table.page.len(parseInt($(this).val())).draw();
    });

    // Refresh table functionality
    $('#refresh-table').on('click', function() {
        table.ajax.reload();
        refreshStats();
    });

    // Modal handlers
    $('#fetch-news-btn').on('click', function() {
        $('#fetch-news-modal').removeClass('hidden');
    });

    $('#generate-news-btn').on('click', function() {
        $('#generate-content-modal').removeClass('hidden');
    });

    // Close modals
    $('#close-fetch-modal, #cancel-fetch').on('click', function() {
        $('#fetch-news-modal').addClass('hidden');
    });

    $('#close-generate-modal, #cancel-generate').on('click', function() {
        $('#generate-content-modal').addClass('hidden');
    });

    // Fetch news
    $('#confirm-fetch').on('click', function() {
        const limit = $('#fetch-limit').val();
        const button = $(this);
        
        button.prop('disabled', true).html('<i class="material-icons animate-spin mr-2">refresh</i> Fetching...');
        
        $.ajax({
            url: '<?php echo e(route("admin.news-integration.fetch-news")); ?>',
            method: 'POST',
            data: {
                limit: limit,
                _token: '<?php echo e(csrf_token()); ?>'
            },
            success: function(response) {
                $('#fetch-news-modal').addClass('hidden');
                
                Swal.fire({
                    icon: 'success',
                    title: 'News Fetched Successfully!',
                    html: `
                        <div class="text-left">
                            <p><strong>Fetched:</strong> ${response.stats.fetched}</p>
                            <p><strong>New:</strong> ${response.stats.new}</p>
                            <p><strong>Duplicates:</strong> ${response.stats.duplicates}</p>
                            <p><strong>Errors:</strong> ${response.stats.errors}</p>
                        </div>
                    `,
                    confirmButtonText: 'OK'
                });
                
                table.ajax.reload();
                refreshStats();
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                Swal.fire({
                    icon: 'error',
                    title: 'Fetch Failed',
                    text: response?.message || 'An error occurred while fetching news.'
                });
            },
            complete: function() {
                button.prop('disabled', false).html('Fetch News');
            }
        });
    });

    // Generate content
    $('#confirm-generate').on('click', function() {
        const limit = $('#generate-limit').val();
        const button = $(this);
        
        button.prop('disabled', true).html('<i class="material-icons animate-spin mr-2">auto_awesome</i> Generating...');
        
        $.ajax({
            url: '<?php echo e(route("admin.news-integration.generate-news")); ?>',
            method: 'POST',
            data: {
                limit: limit,
                _token: '<?php echo e(csrf_token()); ?>'
            },
            success: function(response) {
                $('#generate-content-modal').addClass('hidden');
                
                Swal.fire({
                    icon: 'success',
                    title: 'Content Generated Successfully!',
                    html: `
                        <div class="text-left">
                            <p><strong>Processed:</strong> ${response.results.processed}</p>
                            <p><strong>Successful:</strong> ${response.results.successful}</p>
                            <p><strong>Failed:</strong> ${response.results.failed}</p>
                        </div>
                    `,
                    confirmButtonText: 'OK'
                });
                
                table.ajax.reload();
                refreshStats();
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                Swal.fire({
                    icon: 'error',
                    title: 'Generation Failed',
                    text: response?.message || 'An error occurred while generating content.'
                });
            },
            complete: function() {
                button.prop('disabled', false).html('Generate Content');
            }
        });
    });

    // Refresh statistics
    function refreshStats() {
        $.get('<?php echo e(route("admin.news-integration.stats")); ?>', function(response) {
            if (response.success) {
                $('#stat-total').text(response.stats.total);
                $('#stat-processed').text(response.stats.processed);
                $('#stat-generated').text(response.stats.generated);
                $('#stat-pending').text(response.stats.pending);
            }
        });
    }

    // Custom event handler for processing state
    $(document).ajaxStart(function() {
        $('.dataTables_wrapper').addClass('processing');
    }).ajaxStop(function() {
        $('.dataTables_wrapper').removeClass('processing');
    });

    // Additional event handler for DataTables processing
    table.on('processing.dt', function(e, settings, processing) {
        if (processing) {
            $('.dataTables_wrapper').addClass('processing');
        } else {
            $('.dataTables_wrapper').removeClass('processing');
        }
    });

    // Handle single news generation
    $(document).on('click', '.generate-single', function() {
        const id = $(this).data('id');
        const button = $(this);

        // Show confirmation
        Swal.fire({
            title: 'Generate Content?',
            text: 'This will generate news content for this title using AI.',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Yes, Generate',
            cancelButtonText: 'Cancel',
            confirmButtonColor: '#3B82F6'
        }).then((result) => {
            if (result.isConfirmed) {
                // Disable button and show loading
                button.prop('disabled', true).html('<i class="material-icons animate-spin text-sm">refresh</i>');

                $.ajax({
                    url: '<?php echo e(route("admin.news-integration.generate-single", ":id")); ?>'.replace(':id', id),
                    method: 'POST',
                    data: {
                        _token: '<?php echo e(csrf_token()); ?>'
                    },
                    success: function(response) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Content Generated!',
                            text: 'News content has been generated successfully.',
                            confirmButtonText: 'OK'
                        });

                        // Refresh table and stats
                        table.ajax.reload();
                        refreshStats();
                    },
                    error: function(xhr) {
                        const response = xhr.responseJSON;
                        Swal.fire({
                            icon: 'error',
                            title: 'Generation Failed',
                            text: response?.message || 'An error occurred while generating content.'
                        });

                        // Re-enable button
                        button.prop('disabled', false).html('<i class="material-icons text-sm">auto_awesome</i>');
                    }
                });
            }
        });
    });

    // Auto-refresh stats every 30 seconds
    setInterval(refreshStats, 30000);
     // Modal handling
    const modal = document.getElementById('fetch-news-modal');
    const backdrop = document.getElementById('fetch-modal-backdrop');
    const fetchBtn = document.getElementById('fetch-news-btn');
    const cancelBtn = document.getElementById('fetch-modal-cancel');
    const submitBtn = document.getElementById('fetch-modal-submit');
    
    // Open modal
    fetchBtn.addEventListener('click', () => {
        modal.classList.remove('hidden');
    });
    
    // Close modal functions
    const closeModal = () => {
        modal.classList.add('hidden');
    };
    
    cancelBtn.addEventListener('click', closeModal);
    backdrop.addEventListener('click', closeModal);
    
    // Form submission
    submitBtn.addEventListener('click', () => {
        // Get form values
        const limit = document.getElementById('news-limit').value;
        const country = document.getElementById('news-country').value;
        const language = document.getElementById('news-language').value;
        const category = document.getElementById('news-category').value;
        
        // Build query parameters (only include non-empty values)
        let params = new URLSearchParams();
        
        if (limit) params.append('limit', limit);
        if (country) params.append('country', country);
        if (language) params.append('language', language);
        if (category) params.append('category', category);
        
        // Construct the URL
        const url = "<?php echo e(route('api.news.fetch-latest')); ?>" + (params.toString() ? '?' + params.toString() : '');
        
        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="material-icons animate-spin mr-2">refresh</i> Fetching...';
        
        // Make the AJAX request
        $.ajax({
            url: url,
            type: 'GET',
            success: function(response) {
                // Close modal
                closeModal();
                
                // Show success message
                toastr.success('News fetched successfully');
                
                // Refresh the table
                $('#news-integration-table').DataTable().ajax.reload();
                
                // Update stats
                if (response.stats) {
                    $('#stat-total').text(response.stats.total);
                    $('#stat-processed').text(response.stats.processed);
                    $('#stat-generated').text(response.stats.generated);
                    $('#stat-pending').text(response.stats.pending);
                }
            },
            error: function(xhr) {
                // Show error message
                toastr.error(xhr.responseJSON?.message || 'Failed to fetch news');
            },
            complete: function() {
                // Reset button state
                submitBtn.disabled = false;
                submitBtn.innerHTML = 'Fetch News';
            }
        });
    });

});
</script>







<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\projects\client-admin-panel\resources\views/admin/news-integration/index.blade.php ENDPATH**/ ?>