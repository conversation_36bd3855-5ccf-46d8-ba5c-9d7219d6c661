@extends('layouts.frontend')

@section('title', 'Search Results: ' . $query)

@section('content')
<section class="bg-gray-50 dark:bg-gray-900 py-8 container mx-auto transition-colors duration-200">
    <div class="px-4 sm:px-6 lg:px-8">
        <!-- Search Header -->
        <h1 class="relative border-b border-gray-300/70 dark:border-gray-600/70 pb-2.5 text-2xl font-medium text-gray-900 dark:text-white mb-6 before:absolute before:-bottom-px before:left-0 before:h-px before:w-24 before:bg-red-600 dark:before:bg-red-500 before:content-[''] transition-colors duration-200">
            Search Results: {{ $query }}
        </h1>

        <!-- Search Results -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 md:gap-8" id="search-results-container">
            @forelse($articles as $item)
            <div class="flex flex-col md:flex-row items-start gap-4 py-4 border-b border-gray-200 dark:border-gray-700 transition-colors duration-200">
                <a href="{{ route('news.show', $item->slug) }}" class="w-full md:w-1/3 flex-shrink-0">
                    <div class="aspect-w-16 aspect-h-9 bg-gray-100 dark:bg-gray-800 rounded overflow-hidden transition-colors duration-200">
                        <img 
                            src="{{ asset('storage/' . $item->main_image) }}" 
                            alt="{{ $item->title }}" 
                            class="w-full h-full object-cover transform transition duration-500 ease-in-out hover:scale-110"
                            onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                        <!-- Image placeholder -->
                        <div class="absolute inset-0 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center" style="display:none;">
                            <svg class="h-8 w-8 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                        </div>
                    </div>
                </a>
                <div class="ml-0 md:ml-3 flex-1">
                    @if($item->categories->isNotEmpty())
                        <a href="{{ route('category.show', $item->categories->first()->slug) }}" 
                           class="text-xs font-medium uppercase tracking-widest transition-colors duration-300 ease-in-out"
                           style="color: {{ $item->categories->first()->color }}">
                            {{ $item->categories->first()->name }}
                        </a>
                    @else
                        <span class="text-xs font-medium uppercase tracking-widest text-gray-500 dark:text-gray-400">
                            Uncategorized
                        </span>
                    @endif
                    <h2 class="mt-2 text-xl font-semibold text-gray-900 dark:text-white transition-colors duration-200">
                        <a href="{{ route('news.show', $item->slug) }}" class="hover:text-red-600 dark:hover:text-red-500 transition-colors duration-300 ease-in-out">
                            {{ $item->title }}
                        </a>
                    </h2>
                    <div class="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400 transition-colors duration-200">
                        <span class="inline-flex items-center">
                            <svg class="mr-1.5 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            {{ Carbon\Carbon::parse($item->published_at)->format('M d, Y') }}
                        </span>
                    </div>
                </div>
            </div>
            @empty
            <div class="col-span-2 text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No results found</h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">We couldn't find any articles matching your search.</p>
            </div>
            @endforelse
        </div>

        <div id="loading-indicator" class="text-center py-6 hidden">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-red-600"></div>
        </div>

        @if(count($articles) > 0)
            <div id="load-more-container" class="mt-8 flex justify-center">
                <button id="load-more-btn" class="px-6 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors duration-200">
                    Load More
                </button>
            </div>
        @endif

        <div id="end-of-content" class="text-center py-6 hidden">
            <p class="text-gray-500 dark:text-gray-400">No more results to load</p>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        let offset = {{ count($articles) }};
        const limit = 10;
        let loading = false;
        let hasMore = true;

        const loadingIndicator = document.getElementById('loading-indicator');
        const loadMoreBtn = document.getElementById('load-more-btn');
        const loadMoreContainer = document.getElementById('load-more-container');
        const endOfContent = document.getElementById('end-of-content');
        const searchResultsContainer = document.getElementById('search-results-container');

        function loadMoreResults() {
            if (loading || !hasMore) return;

            loading = true;
            loadingIndicator.classList.remove('hidden');
            loadMoreBtn.disabled = true;

            // Get CSRF token
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            
            // Fetch more results
            fetch('{{ route("search.load-more") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken,
                    'Accept': 'application/json'
                },
                body: JSON.stringify({ 
                    query: "{{ $query }}",
                    offset: offset,
                    limit: limit
                })
            })
            .then(response => response.json())
            .then(data => {
                loadingIndicator.classList.add('hidden');
                loadMoreBtn.disabled = false;

                if (data.html) {
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = data.html;
                    
                    // Append each child individually to maintain event listeners
                    while (tempDiv.firstChild) {
                        searchResultsContainer.appendChild(tempDiv.firstChild);
                    }
                    
                    // Update offset
                    offset += limit;
                }
                
                hasMore = data.hasMore;
                if (!hasMore) {
                    loadMoreContainer.classList.add('hidden');
                    endOfContent.classList.remove('hidden');
                }

                loading = false;
            })
            .catch(error => {
                console.error('Error loading more results:', error);
                loadingIndicator.classList.add('hidden');
                loadMoreBtn.disabled = false;
                loading = false;
            });
        }

        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', loadMoreResults);
        }
    });
</script>
@endpush