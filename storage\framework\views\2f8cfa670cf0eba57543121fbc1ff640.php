

<?php $__env->startSection('title', 'Contact Us'); ?>

<?php $__env->startSection('meta'); ?>
<meta name="description" content="Get in touch with our team. Contact us for news tips, feedback, partnerships, or general inquiries. We're here to help.">
<meta name="keywords" content="contact us, news tips, feedback, support, journalism, media inquiries">
<meta name="robots" content="index, follow">
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .contact-card {
        transition: all 0.3s ease;
    }

    .contact-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .form-input {
        transition: all 0.3s ease;
    }

    .form-input:focus {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
    }

    .contact-method {
        position: relative;
        overflow: hidden;
    }

    .contact-method::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transition: left 0.5s ease;
    }

    .contact-method:hover::before {
        left: 100%;
    }

    .map-container {
        position: relative;
        height: 300px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 0.5rem;
        overflow: hidden;
    }

    .map-placeholder {
        position: absolute;
        inset: 0;
        display: flex;
        items-center;
        justify-content: center;
        color: white;
        font-size: 1.125rem;
        font-weight: 600;
    }

    @media (max-width: 640px) {
        .contact-content {
            font-size: 0.9rem;
            line-height: 1.6;
        }

        .contact-content h2 {
            font-size: 1.5rem !important;
        }

        .contact-content h3 {
            font-size: 1.25rem !important;
        }

        .contact-grid {
            grid-template-columns: 1fr !important;
        }

        .form-input {
            font-size: 16px; /* Prevents zoom on iOS */
        }
    }

    /* Force text colors for better visibility */
    .contact-content p {
        color: #374151 !important;
    }

    .dark .contact-content p {
        color: #d1d5db !important;
    }

    .contact-content strong {
        color: #111827 !important;
    }

    .dark .contact-content strong {
        color: #ffffff !important;
    }

    .contact-content li {
        color: #374151 !important;
    }

    .dark .contact-content li {
        color: #d1d5db !important;
    }

    .contact-content h2 {
        color: #111827 !important;
    }

    .dark .contact-content h2 {
        color: #ffffff !important;
    }

    .contact-content h3 {
        color: #111827 !important;
    }

    .dark .contact-content h3 {
        color: #ffffff !important;
    }

    .contact-content label {
        color: #374151 !important;
    }

    .dark .contact-content label {
        color: #d1d5db !important;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<!-- Header Section -->
<div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 py-8 md:py-12 mb-6 md:mb-8">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto text-center">
            <h1 class="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-3 md:mb-4">Contact Us</h1>
            <p class="text-base md:text-lg text-gray-600 dark:text-gray-300 mb-4 md:mb-6">
                We'd love to hear from you. Send us a message and we'll respond as soon as possible.
            </p>
            <div class="flex flex-col sm:flex-row sm:flex-wrap justify-center gap-3 sm:gap-6 text-sm text-gray-500 dark:text-gray-400">
                <div class="flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="font-medium">Response within 24 hours</span>
                </div>
                <div class="flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="font-medium">Professional Support</span>
                </div>
                <div class="flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <span class="font-medium">Multiple Contact Methods</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="container mx-auto px-4 pb-16">
    <div class="max-w-7xl mx-auto">
        <!-- Contact Methods Grid -->
        <div class="contact-grid grid lg:grid-cols-3 gap-6 md:gap-8 mb-12">
            <!-- General Inquiries -->
            <div class="contact-card contact-method bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 text-center">
                <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">General Inquiries</h3>
                <p class="text-sm text-gray-600 dark:text-gray-300 mb-4">Questions about our content, services, or general information.</p>
                
            </div>

            <!-- News Tips -->
            <div class="contact-card contact-method bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 text-center">
                <div class="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">News Tips</h3>
                <p class="text-sm text-gray-600 dark:text-gray-300 mb-4">Have a story tip or breaking news? We want to hear from you.</p>
                
            </div>

            <!-- Media & Press -->
            <div class="contact-card contact-method bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 text-center">
                <div class="w-16 h-16 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Media & Press</h3>
                <p class="text-sm text-gray-600 dark:text-gray-300 mb-4">Press inquiries, interviews, and media partnerships.</p>
                
            </div>
        </div>

        <!-- Contact Form and Info Section -->
        <div class="grid lg:grid-cols-2 gap-8 lg:gap-12 mb-12">
            <!-- Contact Form -->
            <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 md:p-8">
                <div class="contact-content">
                    <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Send us a Message</h2>

                    <form action="#" method="POST" class="space-y-6">
                        <?php echo csrf_field(); ?>

                        <!-- Name and Email Row -->
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Full Name *
                                </label>
                                <input type="text" id="name" name="name" required
                                    class="form-input w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-all duration-300"
                                    placeholder="Your full name">
                            </div>
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Email Address *
                                </label>
                                <input type="email" id="email" name="email" required
                                    class="form-input w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-all duration-300"
                                    placeholder="<EMAIL>">
                            </div>
                        </div>

                        <!-- Subject -->
                        <div>
                            <label for="subject" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Subject *
                            </label>
                            <select id="subject" name="subject" required
                                class="form-input w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-all duration-300">
                                <option value="">Select a subject</option>
                                <option value="general">General Inquiry</option>
                                <option value="news-tip">News Tip</option>
                                <option value="press">Press/Media Inquiry</option>
                                <option value="partnership">Partnership Opportunity</option>
                                <option value="technical">Technical Support</option>
                                <option value="feedback">Feedback</option>
                                <option value="other">Other</option>
                            </select>
                        </div>

                        <!-- Phone (Optional) -->
                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Phone Number (Optional)
                            </label>
                            <input type="tel" id="phone" name="phone"
                                class="form-input w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-all duration-300"
                                placeholder="+****************">
                        </div>

                        <!-- Message -->
                        <div>
                            <label for="message" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Message *
                            </label>
                            <textarea id="message" name="message" rows="6" required
                                class="form-input w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-all duration-300 resize-vertical"
                                placeholder="Tell us how we can help you..."></textarea>
                        </div>

                        <!-- Privacy Notice -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <div class="flex items-start">
                                <input type="checkbox" id="privacy" name="privacy" required
                                    class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="privacy" class="ml-3 text-sm text-gray-600 dark:text-gray-300">
                                    I agree to the <a href="<?php echo e(route('privacy-policy')); ?>" class="text-blue-600 dark:text-blue-400 hover:underline">Privacy Policy</a> and consent to the processing of my personal data for the purpose of responding to my inquiry.
                                </label>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <button type="submit"
                            class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            <span class="flex items-center justify-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                </svg>
                                Send Message
                            </span>
                        </button>
                    </form>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="space-y-6">
                <!-- Office Information -->
                <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Office Information</h3>
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-gray-400 mt-1 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <div>
                                <p class="font-medium text-gray-900 dark:text-white">Address</p>
                                <p class="text-sm text-gray-600 dark:text-gray-300">
                                    123 News Street<br>
                                    Media District<br>
                                    City, State 12345
                                </p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-gray-400 mt-1 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                            <div>
                                <p class="font-medium text-gray-900 dark:text-white">Phone</p>
                                <p class="text-sm text-gray-600 dark:text-gray-300">+****************</p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-gray-400 mt-1 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <p class="font-medium text-gray-900 dark:text-white">Business Hours</p>
                                <p class="text-sm text-gray-600 dark:text-gray-300">
                                    Monday - Friday: 9:00 AM - 6:00 PM<br>
                                    Saturday: 10:00 AM - 4:00 PM<br>
                                    Sunday: Closed
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Map Placeholder -->
                <div class="map-container">
                    <div class="map-placeholder">
                        <div class="text-center">
                            <svg class="w-12 h-12 mx-auto mb-2 opacity-75" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <p>Interactive Map</p>
                            <p class="text-sm opacity-75">Click to view location</p>
                        </div>
                    </div>
                </div>

                <!-- Social Media -->
                
            </div>
        </div>

        <!-- FAQ Section -->
        <section class="mb-12">
            <div class="text-center mb-8">
                <h2 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4">Frequently Asked Questions</h2>
                <p class="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                    Find quick answers to common questions about our services and policies.
                </p>
            </div>

            <div class="grid md:grid-cols-2 gap-6">
                <!-- FAQ Item 1 -->
                <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">How quickly do you respond to inquiries?</h3>
                    <p class="text-gray-600 dark:text-gray-300 text-sm">
                        We aim to respond to all inquiries within 24 hours during business days. Urgent news tips may receive faster responses.
                    </p>
                </div>

                <!-- FAQ Item 2 -->
                <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Can I submit anonymous news tips?</h3>
                    <p class="text-gray-600 dark:text-gray-300 text-sm">
                        Yes, we accept anonymous tips. You can use our secure tip line or contact form without providing personal information.
                    </p>
                </div>

                <!-- FAQ Item 3 -->
                <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Do you offer press credentials?</h3>
                    <p class="text-gray-600 dark:text-gray-300 text-sm">
                        Press credentials are available for verified journalists. Contact our media relations team with your request and credentials.
                    </p>
                </div>

                <!-- FAQ Item 4 -->
                <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">How can I report an error in an article?</h3>
                    <p class="text-gray-600 dark:text-gray-300 text-sm">
                        We take accuracy seriously. Please contact us with specific details about the error, and we'll investigate and correct it promptly.
                    </p>
                </div>

                <!-- FAQ Item 5 -->
                <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Can I republish your content?</h3>
                    <p class="text-gray-600 dark:text-gray-300 text-sm">
                        Content republication requires permission. Please contact our licensing team with details about your intended use.
                    </p>
                </div>

                <!-- FAQ Item 6 -->
                <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">How do I subscribe to your newsletter?</h3>
                    <p class="text-gray-600 dark:text-gray-300 text-sm">
                        You can subscribe to our newsletter through the signup form on our homepage or by contacting us directly.
                    </p>
                </div>
            </div>
        </section>

        <!-- Emergency Contact Section -->
        <section class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6 md:p-8 text-center">
            <div class="max-w-2xl mx-auto">
                <div class="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <h2 class="text-xl md:text-2xl font-bold text-red-900 dark:text-red-100 mb-4">Breaking News & Urgent Tips</h2>
                <p class="text-red-800 dark:text-red-200 mb-6">
                    For time-sensitive breaking news or urgent story tips that require immediate attention, use our emergency contact line.
                </p>
                <div class="flex flex-col sm:flex-row justify-center gap-4">
                    <a href="tel:+15551234567" class="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                        📞 Emergency Hotline: +****************
                    </a>
                    <a href="mailto:urgent{{ str_replace(['http://', 'https://'], '', config('app.url')) }}" class="border-2 border-red-600 text-red-600 dark:text-red-400 hover:bg-red-600 hover:text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                        📧 urgent{{ str_replace(['http://', 'https://'], '', config('app.url')) }}
                    </a>
                </div>
            </div>
        </section>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation and enhancement
    const form = document.querySelector('form');
    const submitButton = form.querySelector('button[type="submit"]');
    const originalButtonText = submitButton.innerHTML;

    form.addEventListener('submit', function(e) {
        e.preventDefault();

        // Show loading state
        submitButton.disabled = true;
        submitButton.innerHTML = `
            <span class="flex items-center justify-center">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Sending...
            </span>
        `;

        // Simulate form submission (replace with actual form handling)
        setTimeout(() => {
            // Reset button
            submitButton.disabled = false;
            submitButton.innerHTML = originalButtonText;

            // Show success message (you can customize this)
            alert('Thank you for your message! We\'ll get back to you soon.');

            // Reset form
            form.reset();
        }, 2000);
    });

    // Enhanced form inputs with floating labels effect
    const inputs = document.querySelectorAll('.form-input');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            if (!this.value) {
                this.parentElement.classList.remove('focused');
            }
        });

        // Check if input has value on page load
        if (input.value) {
            input.parentElement.classList.add('focused');
        }
    });

    // Animate contact cards on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe contact cards
    document.querySelectorAll('.contact-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });

    // Map click handler (placeholder for actual map integration)
    const mapContainer = document.querySelector('.map-container');
    if (mapContainer) {
        mapContainer.addEventListener('click', function() {
            // Replace with actual map integration (Google Maps, OpenStreetMap, etc.)
            alert('Map integration would open here. You can integrate Google Maps, OpenStreetMap, or any other mapping service.');
        });
    }

    // Phone number formatting
    const phoneInput = document.getElementById('phone');
    if (phoneInput) {
        phoneInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length >= 6) {
                value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
            } else if (value.length >= 3) {
                value = value.replace(/(\d{3})(\d{3})/, '($1) $2');
            }
            e.target.value = value;
        });
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.frontend', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\projects\client-admin-panel\resources\views/frontend/pages/contact.blade.php ENDPATH**/ ?>