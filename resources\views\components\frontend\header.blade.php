<header class="bg-white dark:bg-gray-900 shadow-md sticky top-0 z-50 transition-colors duration-200">
    <nav class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
            <!-- Logo -->
            <div class="flex-shrink-0 flex items-center">
                <a href="{{ url('/') }}" class="flex items-center space-x-2">
                    @if(file_exists(public_path('storage/images/logo-color.png')) || file_exists(public_path('storage/images/logo-light.png')))
                        <img src="{{asset('storage/images/logo-color.png')}}"
                            alt="{{ config('app.name', 'Laravel') }} Logo"
                            class="w-25 rounded-lg object-cover logo-image dark:hidden">
                        <img src="{{asset('storage/images/logo-light.png')}}"
                            alt="{{ config('app.name', 'Laravel') }} Logo"
                            class="w-25 rounded-lg object-cover logo-image hidden dark:block">
                    @else
                    <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center logo-container">
                        <span class="text-white font-bold text-lg logo-text">{{ substr(config('app.name', 'Client'), 0, 1) }}</span>
                    </div>
                    @endif
                    <!-- <span class="text-xl font-bold text-gray-900 dark:text-white">{{ config('app.name', 'Laravel') }}</span> -->
                </a>
            </div>

            <!-- Desktop Mega Menu Navigation -->
            <div class="hidden lg:block">
                <div class="ml-6 flex items-center space-x-1">
                    <a href="{{ url('/') }}" class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800 {{ request()->is('/') ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20' : '' }}">
                        Home
                    </a>

                    <!-- Technology Mega Menu -->
                    <div class="relative group" data-mega-menu="technology">
                        <button class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800 group flex items-center" data-mega-trigger>
                            <span>Technology</span>
                            <svg class="ml-1 h-4 w-4 transition-transform duration-200 group-hover:rotate-180" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                        <div class="absolute top-full mt-2 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 opacity-0 invisible transition-all duration-200 ease-out z-50 p-6 group-hover:opacity-100 group-hover:visible" data-mega-content style="max-width: calc(100vw - 2rem); left: 50%; transform: translateX(-50%) translateY(8px); width: 600px;">
                            <div class="grid grid-cols-3 gap-6">
                                <div>
                                    <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-2 uppercase tracking-wide">Latest Tech</h3>
                                    <ul class="space-y-1">
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">AI & Machine Learning</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Blockchain</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Cloud Computing</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Cybersecurity</a></li>
                                    </ul>
                                </div>
                                <div>
                                    <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-2 uppercase tracking-wide">Development</h3>
                                    <ul class="space-y-1">
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Web Development</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Mobile Apps</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">DevOps</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Programming</a></li>
                                    </ul>
                                </div>
                                <div>
                                    <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-2 uppercase tracking-wide">Hardware</h3>
                                    <ul class="space-y-1">
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Smartphones</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Laptops</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Gaming</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">IoT Devices</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Business Mega Menu -->
                    <div class="relative group" data-mega-menu="business">
                        <button class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800 group flex items-center" data-mega-trigger>
                            <span>Business</span>
                            <svg class="ml-1 h-4 w-4 transition-transform duration-200 group-hover:rotate-180" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                        <div class="absolute top-full mt-2 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 opacity-0 invisible transition-all duration-200 ease-out z-50 p-6 group-hover:opacity-100 group-hover:visible" data-mega-content style="max-width: calc(100vw - 2rem); left: 50%; transform: translateX(-50%) translateY(8px); width: 600px;">
                            <div class="grid grid-cols-3 gap-6">
                                <div>
                                    <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-2 uppercase tracking-wide">Startup</h3>
                                    <ul class="space-y-1">
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Funding</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Entrepreneurship</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Business Plans</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Success Stories</a></li>
                                    </ul>
                                </div>
                                <div>
                                    <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-2 uppercase tracking-wide">Finance</h3>
                                    <ul class="space-y-1">
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Investment</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Stock Market</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Cryptocurrency</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Banking</a></li>
                                    </ul>
                                </div>
                                <div>
                                    <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-2 uppercase tracking-wide">Marketing</h3>
                                    <ul class="space-y-1">
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Digital Marketing</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">SEO</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Social Media</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Content Strategy</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Science Mega Menu -->
                    <div class="relative group" data-mega-menu="science">
                        <button class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800 group flex items-center" data-mega-trigger>
                            <span>Science</span>
                            <svg class="ml-1 h-4 w-4 transition-transform duration-200 group-hover:rotate-180" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                        <div class="absolute top-full mt-2 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 opacity-0 invisible transition-all duration-200 ease-out z-50 p-6 group-hover:opacity-100 group-hover:visible" data-mega-content style="max-width: calc(100vw - 2rem); left: 50%; transform: translateX(-50%) translateY(8px); width: 600px;">
                            <div class="grid grid-cols-3 gap-6">
                                <div>
                                    <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-2 uppercase tracking-wide">Research</h3>
                                    <ul class="space-y-1">
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Medical Research</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Space Exploration</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Climate Science</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Physics</a></li>
                                    </ul>
                                </div>
                                <div>
                                    <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-2 uppercase tracking-wide">Innovation</h3>
                                    <ul class="space-y-1">
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Biotechnology</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Renewable Energy</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Nanotechnology</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Robotics</a></li>
                                    </ul>
                                </div>
                                <div>
                                    <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-2 uppercase tracking-wide">Environment</h3>
                                    <ul class="space-y-1">
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Climate Change</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Conservation</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Sustainability</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Wildlife</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Culture Menu -->
                    <a href="#" class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800">Culture</a>

                    <!-- More Menu -->
                    <div class="relative group" data-mega-menu="more">
                        <button class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800 group flex items-center" data-mega-trigger>
                            <span>More</span>
                            <svg class="ml-1 h-4 w-4 transition-transform duration-200 group-hover:rotate-180" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                        <div class="absolute top-full mt-2 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 opacity-0 invisible transition-all duration-200 ease-out z-50 p-6 group-hover:opacity-100 group-hover:visible" data-mega-content style="max-width: calc(100vw - 2rem); left: 50%; transform: translateX(-50%) translateY(8px); width: 400px;">
                            <div class="grid grid-cols-2 gap-6">
                                <div>
                                    <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-2 uppercase tracking-wide">Company</h3>
                                    <ul class="space-y-1">
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">About Us</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Contact</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Careers</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Press</a></li>
                                    </ul>
                                </div>
                                <div>
                                    <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-2 uppercase tracking-wide">Support</h3>
                                    <ul class="space-y-1">
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Help Center</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Privacy Policy</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Terms of Service</a></li>
                                        <li><a href="#" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50">Advertise</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>



            <!-- Theme Toggle & Search Button (right side on mobile) -->
            <div class="flex items-center space-x-3">
                <!-- Theme Toggle -->
                <div class="relative" data-theme-dropdown>
                    <button data-theme-dropdown-button class="flex items-center justify-center w-10 h-10 rounded-full bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-100 dark:focus:ring-red-900" aria-label="Toggle theme">
                        <!-- Light mode icon -->
                        <svg id="theme-icon-light" class="h-5 w-5 text-gray-600 dark:text-gray-400 hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                        <!-- Dark mode icon -->
                        <svg id="theme-icon-dark" class="h-5 w-5 text-gray-600 dark:text-gray-400 hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                        </svg>
                        <!-- System mode icon -->
                        <svg id="theme-icon-system" class="h-5 w-5 text-gray-600 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                    </button>
                    <div data-theme-dropdown-menu class="hidden absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-50 transition-all duration-100">
                        <button data-theme-option="light" class="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                            <svg class="h-4 w-4 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                            </svg>
                            Light
                        </button>
                        <button data-theme-option="dark" class="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                            <svg class="h-4 w-4 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                            </svg>
                            Dark
                        </button>
                        <button data-theme-option="system" class="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                            <svg class="h-4 w-4 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                            System
                        </button>
                    </div>
                </div>

                <!-- Search Button -->
                <button type="button"
                    id="search-button"
                    class="flex items-center justify-center w-10 h-10 rounded-full bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-100 dark:focus:ring-red-900"
                    aria-label="Search">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="h-5 w-5 text-gray-600 dark:text-gray-400">
                        <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path>
                    </svg>
                </button>

                <!-- Mobile menu button (rightmost position) -->
                <div class="lg:hidden">
                    <button type="button"
                        id="mobile-nav-button"
                        class="group relative flex cursor-pointer items-center justify-center rounded-full bg-gray-50 dark:bg-gray-800 p-3 ring-1 ring-gray-900/5 dark:ring-gray-100/5 transition duration-300 ease-in-out hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none"
                        aria-expanded="false">
                        <span class="sr-only">Open main menu</span>
                        <span class="relative h-3.5 w-4">
                            <span class="absolute block h-0.5 rotate-0 transform rounded-full bg-gray-600 dark:bg-gray-400 opacity-100 transition-all duration-300 ease-in-out group-hover:bg-gray-900 dark:group-hover:bg-gray-100 left-0 top-0 w-full" id="menu-line-1"></span>
                            <span class="absolute left-0 top-1.5 block h-0.5 w-full rotate-0 transform rounded-full bg-gray-600 dark:bg-gray-400 opacity-100 transition-all duration-300 ease-in-out group-hover:bg-gray-900 dark:group-hover:bg-gray-100" id="menu-line-2"></span>
                            <span class="absolute left-0 top-1.5 block h-0.5 w-full rotate-0 transform rounded-full bg-gray-600 dark:bg-gray-400 opacity-100 transition-all duration-300 ease-in-out group-hover:bg-gray-900 dark:group-hover:bg-gray-100" id="menu-line-3"></span>
                            <span class="absolute block h-0.5 rotate-0 transform rounded-full bg-gray-600 dark:bg-gray-400 opacity-100 transition-all duration-300 ease-in-out group-hover:bg-gray-900 dark:group-hover:bg-gray-100 left-0 top-3 w-full" id="menu-line-4"></span>
                        </span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Slide Menu Overlay -->
        <div id="mobile-menu-overlay" class="fixed inset-0 z-40 bg-[rgba(0,0,0,0.5)] transition-opacity duration-300 opacity-0 pointer-events-none lg:hidden"></div>

        <!-- Mobile Left Slide Menu -->
        <div id="mobile-slide-menu" class="fixed top-0 left-0 z-50 h-full w-72 bg-white dark:bg-gray-900 shadow-xl transform -translate-x-full transition-transform duration-300 ease-in-out lg:hidden flex flex-col">
            <!-- Mobile Menu Header -->
            <div class="bg-gradient-to-r from-blue-50 to-transparent dark:from-blue-900/20 dark:to-transparent flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center shadow-lg">
                        <span class="text-white font-bold text-sm">{{ substr(config('app.name', 'Client'), 0, 1) }}</span>
                    </div>
                    <div>
                        <span class="text-sm font-bold text-gray-900 dark:text-white">{{ config('app.name', 'Laravel') }}</span>
                        <p class="text-xs text-gray-500 dark:text-gray-400">Menu</p>
                    </div>
                </div>
                <button id="mobile-menu-close" class="p-1.5 rounded-lg text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200">
                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <!-- Mobile Menu Content -->
            <div class="flex-1 overflow-y-auto overflow-x-hidden">
                <nav class="px-3 py-4 space-y-1 min-h-full">
                    <!-- Home -->
                    <a href="{{ url('/') }}" class="flex items-center space-x-2 px-3 py-2.5 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-all duration-200 font-medium border-l-3 border-transparent hover:bg-gradient-to-r hover:from-blue-50 hover:to-transparent dark:hover:from-blue-900/20 dark:hover:to-transparent hover:border-blue-500 {{ request()->is('/') ? 'bg-gradient-to-r from-blue-50 to-blue-50/50 dark:from-blue-900/30 dark:to-blue-900/10 text-blue-600 dark:text-blue-400 border-blue-500' : '' }}">
                        <svg class="h-4 w-4 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                        </svg>
                        <span>Home</span>
                    </a>

                    <!-- Technology Section -->
                    <div class="">
                        <button class="flex items-center justify-between w-full px-3 py-2.5 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-all duration-200 font-medium border-l-3 border-transparent hover:bg-gradient-to-r hover:from-gray-50 hover:to-transparent dark:hover:from-gray-800 dark:hover:to-transparent hover:border-gray-400" data-section="technology">
                            <span class="flex items-center space-x-2">
                                <svg class="h-4 w-4 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                                <span>Technology</span>
                            </span>
                            <svg class="h-3 w-3 flex-shrink-0 transition-transform duration-300 ease-in-out" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                        <div class="max-h-0 overflow-hidden transition-all duration-300 ease-in-out ml-3 border-l-2 border-gray-200 dark:border-gray-700" data-content="technology">
                            <div class="pl-4 space-y-0.5 py-2">
                                <a href="#" class="block px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-all duration-200 relative hover:bg-gradient-to-r hover:from-blue-50 hover:to-transparent dark:hover:from-blue-900/20 dark:hover:to-transparent">AI & Machine Learning</a>
                                <a href="#" class="block px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-all duration-200 relative hover:bg-gradient-to-r hover:from-blue-50 hover:to-transparent dark:hover:from-blue-900/20 dark:hover:to-transparent">Blockchain</a>
                                <a href="#" class="block px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-all duration-200 relative hover:bg-gradient-to-r hover:from-blue-50 hover:to-transparent dark:hover:from-blue-900/20 dark:hover:to-transparent">Cloud Computing</a>
                                <a href="#" class="block px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-all duration-200 relative hover:bg-gradient-to-r hover:from-blue-50 hover:to-transparent dark:hover:from-blue-900/20 dark:hover:to-transparent">Cybersecurity</a>
                                <a href="#" class="block px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-all duration-200 relative hover:bg-gradient-to-r hover:from-blue-50 hover:to-transparent dark:hover:from-blue-900/20 dark:hover:to-transparent">Web Development</a>
                                <a href="#" class="block px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-all duration-200 relative hover:bg-gradient-to-r hover:from-blue-50 hover:to-transparent dark:hover:from-blue-900/20 dark:hover:to-transparent">Mobile Apps</a>
                            </div>
                        </div>
                    </div>

                    <!-- Business Section -->
                    <div class="">
                        <button class="flex items-center justify-between w-full px-3 py-2.5 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-all duration-200 font-medium border-l-3 border-transparent hover:bg-gradient-to-r hover:from-gray-50 hover:to-transparent dark:hover:from-gray-800 dark:hover:to-transparent hover:border-gray-400" data-section="business">
                            <span class="flex items-center space-x-2">
                                <svg class="h-4 w-4 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                </svg>
                                <span>Business</span>
                            </span>
                            <svg class="h-3 w-3 flex-shrink-0 transition-transform duration-300 ease-in-out" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                        <div class="max-h-0 overflow-hidden transition-all duration-300 ease-in-out ml-3 border-l-2 border-gray-200 dark:border-gray-700" data-content="business">
                            <div class="pl-4 space-y-0.5 py-2">
                                <a href="#" class="block px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-all duration-200 relative hover:bg-gradient-to-r hover:from-blue-50 hover:to-transparent dark:hover:from-blue-900/20 dark:hover:to-transparent">Startup</a>
                                <a href="#" class="block px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-all duration-200 relative hover:bg-gradient-to-r hover:from-blue-50 hover:to-transparent dark:hover:from-blue-900/20 dark:hover:to-transparent">Funding</a>
                                <a href="#" class="block px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-all duration-200 relative hover:bg-gradient-to-r hover:from-blue-50 hover:to-transparent dark:hover:from-blue-900/20 dark:hover:to-transparent">Investment</a>
                                <a href="#" class="block px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-all duration-200 relative hover:bg-gradient-to-r hover:from-blue-50 hover:to-transparent dark:hover:from-blue-900/20 dark:hover:to-transparent">Stock Market</a>
                                <a href="#" class="block px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-all duration-200 relative hover:bg-gradient-to-r hover:from-blue-50 hover:to-transparent dark:hover:from-blue-900/20 dark:hover:to-transparent">Digital Marketing</a>
                                <a href="#" class="block px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-all duration-200 relative hover:bg-gradient-to-r hover:from-blue-50 hover:to-transparent dark:hover:from-blue-900/20 dark:hover:to-transparent">SEO</a>
                            </div>
                        </div>
                    </div>

                    <!-- Science Section -->
                    <div class="">
                        <button class="flex items-center justify-between w-full px-3 py-2.5 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-all duration-200 font-medium border-l-3 border-transparent hover:bg-gradient-to-r hover:from-gray-50 hover:to-transparent dark:hover:from-gray-800 dark:hover:to-transparent hover:border-gray-400" data-section="science">
                            <span class="flex items-center space-x-2">
                                <svg class="h-4 w-4 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                                </svg>
                                <span>Science</span>
                            </span>
                            <svg class="h-3 w-3 flex-shrink-0 transition-transform duration-300 ease-in-out" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                        <div class="max-h-0 overflow-hidden transition-all duration-300 ease-in-out ml-3 border-l-2 border-gray-200 dark:border-gray-700" data-content="science">
                            <div class="pl-4 space-y-0.5 py-2">
                                <a href="#" class="block px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-all duration-200 relative hover:bg-gradient-to-r hover:from-blue-50 hover:to-transparent dark:hover:from-blue-900/20 dark:hover:to-transparent">Medical Research</a>
                                <a href="#" class="block px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-all duration-200 relative hover:bg-gradient-to-r hover:from-blue-50 hover:to-transparent dark:hover:from-blue-900/20 dark:hover:to-transparent">Space Exploration</a>
                                <a href="#" class="block px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-all duration-200 relative hover:bg-gradient-to-r hover:from-blue-50 hover:to-transparent dark:hover:from-blue-900/20 dark:hover:to-transparent">Climate Science</a>
                                <a href="#" class="block px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-all duration-200 relative hover:bg-gradient-to-r hover:from-blue-50 hover:to-transparent dark:hover:from-blue-900/20 dark:hover:to-transparent">Biotechnology</a>
                                <a href="#" class="block px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-all duration-200 relative hover:bg-gradient-to-r hover:from-blue-50 hover:to-transparent dark:hover:from-blue-900/20 dark:hover:to-transparent">Renewable Energy</a>
                                <a href="#" class="block px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-all duration-200 relative hover:bg-gradient-to-r hover:from-blue-50 hover:to-transparent dark:hover:from-blue-900/20 dark:hover:to-transparent">Environment</a>
                            </div>
                        </div>
                    </div>

                    <!-- Culture -->
                    <a href="#" class="flex items-center space-x-2 px-3 py-2.5 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-all duration-200 font-medium border-l-3 border-transparent hover:bg-gradient-to-r hover:from-blue-50 hover:to-transparent dark:hover:from-blue-900/20 dark:hover:to-transparent hover:border-blue-500">
                        <svg class="h-4 w-4 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2M9 12l2 2 4-4" />
                        </svg>
                        <span>Culture</span>
                    </a>

                    <!-- More Section -->
                    <div class="">
                        <button class="flex items-center justify-between w-full px-3 py-2.5 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-all duration-200 font-medium border-l-3 border-transparent hover:bg-gradient-to-r hover:from-gray-50 hover:to-transparent dark:hover:from-gray-800 dark:hover:to-transparent hover:border-gray-400" data-section="more">
                            <span class="flex items-center space-x-2">
                                <svg class="h-4 w-4 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                                </svg>
                                <span>More</span>
                            </span>
                            <svg class="h-3 w-3 flex-shrink-0 transition-transform duration-300 ease-in-out" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                        <div class="max-h-0 overflow-hidden transition-all duration-300 ease-in-out ml-3 border-l-2 border-gray-200 dark:border-gray-700" data-content="more">
                            <div class="pl-4 space-y-0.5 py-2">
                                <a href="#" class="block px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-all duration-200 relative hover:bg-gradient-to-r hover:from-blue-50 hover:to-transparent dark:hover:from-blue-900/20 dark:hover:to-transparent">About Us</a>
                                <a href="#" class="block px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-all duration-200 relative hover:bg-gradient-to-r hover:from-blue-50 hover:to-transparent dark:hover:from-blue-900/20 dark:hover:to-transparent">Contact</a>
                                <a href="#" class="block px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-all duration-200 relative hover:bg-gradient-to-r hover:from-blue-50 hover:to-transparent dark:hover:from-blue-900/20 dark:hover:to-transparent">Privacy Policy</a>
                                <a href="#" class="block px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-all duration-200 relative hover:bg-gradient-to-r hover:from-blue-50 hover:to-transparent dark:hover:from-blue-900/20 dark:hover:to-transparent">Terms of Service</a>
                                <a href="#" class="block px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-all duration-200 relative hover:bg-gradient-to-r hover:from-blue-50 hover:to-transparent dark:hover:from-blue-900/20 dark:hover:to-transparent">Help Center</a>
                                <a href="#" class="block px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-all duration-200 relative hover:bg-gradient-to-r hover:from-blue-50 hover:to-transparent dark:hover:from-blue-900/20 dark:hover:to-transparent">Advertise</a>
                            </div>
                        </div>
                    </div>
                </nav>
            </div>
        </div>
    </nav>

    <!-- Search Popup Modal -->
    <div id="search-modal" class="fixed inset-0 z-50 hidden overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <!-- Background overlay -->
        <div class="flex min-h-screen items-center justify-center px-4 py-6 text-center">
            <div class="fixed inset-0 bg-black/50 dark:bg-black/70 transition-opacity duration-300" aria-hidden="true" id="search-overlay"></div>

            <!-- Modal panel -->
            <div id="search-modal-panel" class="relative inline-block w-full max-w-2xl transform overflow-hidden rounded-lg bg-white dark:bg-gray-800 text-left shadow-xl transition-all duration-300 ease-out -translate-y-4 opacity-0">
                <!-- Modal header -->
                <div class="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white" id="modal-title">Search News</h3>
                    <button type="button"
                        id="close-search-modal"
                        class="rounded-md bg-white dark:bg-gray-800 text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800">
                        <span class="sr-only">Close</span>
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <!-- Search form -->
                <div class="sm:px-6 sm:py-4 p-0">
                    <form id="search-form">
                        <div class="relative ">
                            <div class="absolute inset-y-0 left-0 flex items-center pl-3">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="h-5 w-5 text-gray-400">
                                    <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <input type="text"
                                id="search-input"
                                class="block w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 pl-10 pr-4 py-3 text-sm text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 "
                                placeholder="Search for articles, topics, or keywords..."
                                autocomplete="off">
                        </div>
                    </form>
                </div>

                <!-- Search results -->
                <div id="search-results" class="max-h-96 overflow-y-auto border-t border-gray-200 dark:border-gray-700">
                    <!-- Default state -->
                    <div id="search-placeholder" class="px-6 py-8 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Start typing to search</h3>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Search through our articles and find what you're looking for.</p>
                    </div>

                    <!-- Loading state -->
                    <div id="search-loading" class="hidden px-6 py-8 text-center">
                        <div class="flex flex-col items-center">
                            <!-- Animated loading spinner -->
                            <div class="relative">
                                <div class="w-12 h-12 border-4 border-gray-200 dark:border-gray-600 rounded-full">
                                    <div class="absolute top-0 left-0 w-12 h-12 border-4 border-transparent border-t-blue-500 dark:border-t-blue-400 rounded-full search-loading-spinner"></div>
                                </div>
                            </div>
                            <h3 class="mt-4 text-sm font-medium text-gray-900 dark:text-white">Searching...</h3>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Please wait while we find relevant articles.</p>
                        </div>
                    </div>

                    <!-- Search results will be populated here -->
                    <div id="search-results-list" class="hidden">
                        <!-- Results will be dynamically inserted here -->
                    </div>

                    <!-- No results state -->
                    <div id="no-results" class="hidden px-6 py-8 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.137 0-4.146-.832-5.636-2.364M12 15l3.031-3.031" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No results found</h3>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Try adjusting your search terms or browse our categories.</p>
                    </div>
                </div>

                <!-- Quick links -->
                <div class="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 px-6 py-4">
                    <h4 class="text-xs font-medium uppercase tracking-wide text-gray-500 dark:text-gray-400">Popular Categories</h4>
                    <div class="mt-2 flex flex-wrap gap-2">
                        <a href="#" class="inline-flex items-center rounded-full bg-white dark:bg-gray-600 px-3 py-1 text-xs font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors duration-200">Technology</a>
                        <a href="#" class="inline-flex items-center rounded-full bg-white dark:bg-gray-600 px-3 py-1 text-xs font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors duration-200">Startup</a>
                        <a href="#" class="inline-flex items-center rounded-full bg-white dark:bg-gray-600 px-3 py-1 text-xs font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors duration-200">Science</a>
                        <a href="#" class="inline-flex items-center rounded-full bg-white dark:bg-gray-600 px-3 py-1 text-xs font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors duration-200">Culture</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>
