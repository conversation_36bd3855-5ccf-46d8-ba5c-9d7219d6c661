<?php $__env->startSection('title', $news->title); ?>

<?php $__env->startSection('meta'); ?>
<meta name="description" content="<?php echo e($news->excerpt); ?>">
<?php if($news->og_image): ?>
<meta property="og:image" content="<?php echo e(asset('storage/' . $news->og_image)); ?>">
<?php else: ?>
<meta property="og:image" content="<?php echo e(asset('storage/' . $news->main_image)); ?>">
<?php endif; ?>
<meta property="og:title" content="<?php echo e($news->title); ?>">
<meta property="og:description" content="<?php echo e($news->excerpt); ?>">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-8">
    <article class=" mx-auto">
        <!-- Categories -->
        <div class="mb-4">
            <?php $__currentLoopData = $news->categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <a href="<?php echo e(route('category.show', $category->slug)); ?>"
                class="category-badge bg-gray-200 dark:bg-gray-400 inline-block text-sm font-medium uppercase tracking-widest px-3 py-1 rounded-md mr-2 transition-colors duration-300 hover:opacity-90 text-black">
                <?php echo e($category->name); ?>

            </a>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Title -->
        <h1 class="text-4xl font-bold mb-4 text-gray-900 dark:text-white"><?php echo e($news->title); ?></h1>

        <!-- Publication date -->
        <div class="text-gray-600 dark:text-gray-400 mb-6">
            Published on <?php echo e($news->published_at->format('F d, Y')); ?>

        </div>

        <!-- Main image -->
        <div class="mb-8">
            <img src="<?php echo e(asset('storage/' . $news->main_image)); ?>"
                alt="<?php echo e($news->title); ?>"
                class="w-full sm:w-[60%] mx-auto h-auto rounded-lg">
        </div>

        <!-- Content -->
        <div class="prose prose-lg max-w-none dark:prose-invert prose-img:rounded-lg prose-headings:font-bold prose-a:text-blue-600 dark:prose-a:text-blue-400 mb-8">
            <?php echo $news->description; ?>

        </div>

        <!-- Tags -->
        <?php if($news->tags->count() > 0): ?>
        <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-white">Tags:</h3>
            <div>
                <ul class="flex flex-wrap justify-start gap-2">
                    <?php $__currentLoopData = $news->tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li>
                        <a href="<?php echo e(route('tag.show', $tag->slug)); ?>">
                            <span class="inline-flex items-center rounded-full border border-gray-300/70 dark:border-gray-600/70 bg-transparent px-4 py-1 text-sm font-medium text-gray-800 dark:text-gray-200 transition duration-300 ease-in-out hover:text-red-700 dark:hover:text-red-400 sm:px-4 sm:py-1.5">
                                <?php echo e($tag->name); ?>

                            </span>
                        </a>
                    </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            </div>
        </div>
        <?php endif; ?>
    </article>

    <!-- Related news -->
    <?php if($relatedNews->count() > 0): ?>
    <div class="mt-12 mx-auto">
        <h2 class="relative border-b border-gray-300/70 dark:border-gray-600/70 pb-2.5 text-2xl font-medium text-gray-900 dark:text-white mb-6 before:absolute before:-bottom-px before:left-0 before:h-px before:w-24 before:bg-red-600 dark:before:bg-red-500 before:content-[''] transition-colors duration-200">
            Related News
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-6">
            <?php $__currentLoopData = $relatedNews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <!-- Article -->
            <article class="py-0 flex">
                <a class="article-image w-2/5 flex-shrink-0" href="<?php echo e(route('news.show', $item->slug)); ?>">
                    <div class="group aspect-h-9 aspect-w-16 overflow-hidden rounded bg-gray-100 dark:bg-gray-800 relative">
                        <img alt="<?php echo e($item->title); ?>"
                            loading="lazy"
                            decoding="async"
                            class="rounded object-cover object-center transition duration-300 ease-in-out group-hover:scale-110"
                            style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent"
                            src="<?php echo e(asset('storage/' . $item->main_image)); ?>"
                            onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                        <!-- Image placeholder -->
                        <div class="absolute inset-0 bg-gray-200 rounded flex items-center justify-center" style="display:none;">
                            <svg class="h-8 w-8 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                        </div>
                    </div>
                </a>
                <div class="ml-5 flex-1">
                    <?php if($item->categories->isNotEmpty()): ?>
                        <a class="text-xs font-medium uppercase transition-colors duration-300 hover:opacity-90" 
                           href="<?php echo e(route('category.show', $item->categories->first()->slug)); ?>"
                           style="color: <?= $item->categories->first()->color ?>;">
                            <?php echo e($item->categories->first()->name); ?>

                        </a>
                    <?php endif; ?>
                    <a href="<?php echo e(route('news.show', $item->slug)); ?>">
                        <h3 class="text-xs lg:text-base font-medium leading-normal tracking-normal text-gray-900 dark:text-white decoration-gray-800 decoration-2 transition duration-300 ease-in-out">
                            <?php echo e($item->title); ?>

                        </h3>
                    </a>
                    <p class="mt-2 text-xs text-gray-500 dark:text-gray-400">
                        <?php echo e($item->published_at->format('M d, Y')); ?>

                    </p>
                </div>
            </article>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>












<?php echo $__env->make('layouts.frontend', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\projects\client-admin-panel\resources\views/frontend/news/show.blade.php ENDPATH**/ ?>