<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\LatestNewsTitle;
use App\Models\News;
use App\Services\NewsApiService;
use App\Services\NewsGeneratorService;
use Illuminate\Support\Facades\Log;

class NewsIntegrationController extends Controller
{
    protected $newsApiService;
    protected $newsGeneratorService;

    public function __construct(NewsApiService $newsApiService, NewsGeneratorService $newsGeneratorService)
    {
        $this->newsApiService = $newsApiService;
        $this->newsGeneratorService = $newsGeneratorService;
    }

    /**
     * Fetch latest news from external API
     * 
     * @param int $limit Number of articles to fetch
     * @param string|null $country Country code (e.g. 'us', 'in')
     * @param string|null $language Language code (e.g. 'en')
     * @param string|null $category News category
     * @return \Illuminate\Http\JsonResponse
     */
    public function fetchLatest($limit = 10, $country = null, $language = null, $category = null)
    {
        try {
            $limit = (int) $limit;

            $stats = [
                'fetched' => 0,
                'new' => 0,
                'duplicates' => 0,
                'errors' => 0
            ];

            // Pass all parameters to the service
            $params = [
                'limit' => $limit
            ];
            
            // Only add non-null parameters
            if ($country) $params['country'] = $country;
            if ($language) $params['language'] = $language;
            if ($category) $params['category'] = $category;

            $articles = $this->newsApiService->fetchLatestNews($params);
            $stats['fetched'] = count($articles);

            if (empty($articles)) {
                return response()->json([
                    'success' => true,
                    'message' => 'No articles fetched from API',
                    'stats' => $stats,
                    'timestamp' => now()->toDateTimeString()
                ]);
            }

            // Process each article
            $processed = 0;
            foreach ($articles as $article) {

                if ($processed >= $limit) {
                    break;
                }

                try {
                    // Check if article already exists
                    if (LatestNewsTitle::exists($article['title'])) {
                        $stats['duplicates']++;
                        continue;
                    }
                    // Create new record
                    LatestNewsTitle::create([
                        'title' => $article['title'],
                        'source_published_at' => $article['source_published_at'],
                        'api_response' => $article['api_response'] ?? $article,
                        'is_processed' => true,
                    ]);
                    
                    $stats['new']++;

                } catch (\Exception $e) {
                    $stats['errors']++;
                    Log::error('Error processing article', [
                        'article' => $article,
                        'error' => $e->getMessage()
                    ]);
                }

                $processed++;
            }

            return response()->json([
                'success' => true,
                'message' => 'News fetch completed successfully',
                'stats' => $stats,
                'timestamp' => now()->toDateTimeString()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch news: ' . $e->getMessage(),
                'timestamp' => now()->toDateTimeString()
            ], 500);
        }
    }

    /**
     * Generate content using Gemini AI
     */
    public function generateContent($limit = 10)
    {
        try {
            $limit = (int) $limit;

            // Generate news articles using the service
            $results = $this->newsGeneratorService->generateNewsFromLatestTitles($limit);

            return response()->json([
                'success' => true,
                'message' => 'Content generation completed successfully',
                'results' => $results,
                'timestamp' => now()->toDateTimeString()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate content: ' . $e->getMessage(),
                'timestamp' => now()->toDateTimeString()
            ], 500);
        }
    }

    /**
     * Get system status and statistics
     */
    public function status()
    {
        try {
            $stats = [
                'total_titles' => LatestNewsTitle::count(),
                'processed' => LatestNewsTitle::where('is_processed', true)->count(),
                'generated' => LatestNewsTitle::where('is_generated', true)->count(),
                'pending_generation' => LatestNewsTitle::readyForGeneration()->count(),
                'recent_fetched_24h' => LatestNewsTitle::where('created_at', '>=', now()->subHours(24))->count(),
                'recent_generated_24h' => LatestNewsTitle::where('is_generated', true)
                    ->where('updated_at', '>=', now()->subHours(24))->count(),
                'total_news_articles' => News::count(),
                'timestamp' => now()->toDateTimeString()
            ];

            return response()->json([
                'success' => true,
                'stats' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get status: ' . $e->getMessage(),
                'timestamp' => now()->toDateTimeString()
            ], 500);
        }
    }
}


