@extends('layouts.admin')

@section('title', 'Create User')

@php
    $pageTitle = 'Create New User';
    $pageDescription = 'Add a new user to the system';
    $breadcrumbs = [
        ['title' => 'Dashboard', 'url' => route('admin.dashboard')],
        ['title' => 'Users', 'url' => route('admin.users.index')],
        ['title' => 'Create', 'url' => '#'],
    ];
@endphp

@section('page-header')
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-semibold text-gray-900">{{ $pageTitle }}</h1>
            <p class="mt-2 text-gray-600">{{ $pageDescription }}</p>
        </div>
        <a href="{{ route('admin.users.index') }}" class="material-button material-button-secondary flex items-center">
            <i class="material-icons mr-2">arrow_back</i>
            Back to Users
        </a>
    </div>
@endsection

@section('content')

    <!-- Create User Form -->
    <div class="material-card">
        <form method="POST" action="{{ route('admin.users.store') }}" class="p-6" id="user_form"
            enctype="multipart/form-data">
            @csrf

            <!-- Form Layout - Responsive Grid -->
            <div class="space-y-6">
                <!-- Row 1: Name and Email -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Name Field -->
                    <div class="order-1 lg:order-1">
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                            Full Name <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                <i class="material-icons text-gray-400 text-lg">person</i>
                            </div>
                            <input id="name" name="name" type="text" required value="{{ old('name') }}"
                                class="material-input-with-icon @error('name') material-input-error @enderror"
                                placeholder="Enter full name">
                        </div>
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Email Field -->
                    <div class="order-2 lg:order-2">
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                            Email Address <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                <i class="material-icons text-gray-400 text-lg">email</i>
                            </div>
                            <input id="email" name="email" type="email" required value="{{ old('email') }}"
                                class="material-input-with-icon @error('email') material-input-error @enderror"
                                placeholder="Enter email address">
                        </div>
                        @error('email')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Row 2: Password and Confirm Password -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Password Field -->
                    <div class="order-3 lg:order-1">
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                            Password <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                <i class="material-icons text-gray-400 text-lg">lock</i>
                            </div>
                            <input id="password" name="password" type="password" required autocomplete="new-password"
                                class="material-input-with-icon @error('password') material-input-error @enderror"
                                placeholder="Enter password">
                            <button type="button" id="toggle-password-admin"
                                data-password-toggle="password:password-icon-admin"
                                class="absolute inset-y-0 right-0 px-3 flex items-center z-10 focus:outline-none rounded">
                                <i class="material-icons text-gray-400 hover:text-gray-600 text-lg"
                                    id="password-icon-admin">visibility</i>
                            </button>
                        </div>
                        @error('password')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Confirm Password Field -->
                    <div class="order-4 lg:order-2">
                        <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">
                            Confirm Password <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                <i class="material-icons text-gray-400 text-lg">lock</i>
                            </div>
                            <input id="password_confirmation" name="password_confirmation" type="password" required
                                class="material-input-with-icon" placeholder="Confirm password">
                            <button type="button" id="toggle-password-confirm-admin"
                                data-password-toggle="password_confirmation:password-confirm-icon-admin"
                                class="absolute inset-y-0 right-0 px-3 flex items-center z-10 focus:outline-none rounded">
                                <i class="material-icons text-gray-400 hover:text-gray-600 text-lg"
                                    id="password-confirm-icon-admin">visibility</i>
                            </button>
                        </div>
                        @error('password_confirmation')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Row 3: Role and Status -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Role Field -->
                    <div class="order-5 lg:order-1">
                        <label for="role" class="block text-sm font-medium text-gray-700 mb-2">
                            Role <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                <i class="material-icons text-gray-400 text-lg">verified_user</i>
                            </div>
                            <select id="role" name="role" required
                                class="material-select-with-icon @error('role') material-select-error @enderror">
                                <option value="">Select a role</option>
                                @foreach ($roles as $role)
                                    <option value="{{ $role->id }}" {{ old('role') == $role->id ? 'selected' : '' }}>
                                        {{ $role->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        @error('role')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Status Field -->
                    <div class="order-6 lg:order-2">
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                            Status
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                <i class="material-icons text-gray-400 text-lg">toggle_on</i>
                            </div>
                            <select id="status" name="status"
                                class="material-select-with-icon @error('status') material-select-error @enderror">
                                <option value="active" {{ old('status') == 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                        </div>
                        @error('status')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
                <!-- Row 4: Profile Image (Full Width) -->
                <div class="grid grid-cols-1 gap-6">
                    <div class="order-7">
                        <label for="profile_image" class="block text-sm font-medium text-gray-700 mb-2">
                            Profile Image <small class="text-gray-500">(optional)</small>
                        </label>
                        <div class="mt-1 flex items-center space-x-4">
                            <!-- Image Preview -->
                            <div class="flex-shrink-0">
                                <img id="image-preview"
                                    class="h-20 w-20 rounded-full object-cover border-2 border-gray-300 bg-gray-100"
                                    src="data:image/svg+xml,%3csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100' height='100' fill='%23f3f4f6'/%3e%3ctext x='50%25' y='50%25' font-size='14' text-anchor='middle' dy='.3em' fill='%236b7280'%3eNo Image%3c/text%3e%3c/svg%3e"
                                    alt="Profile preview">
                            </div>
                            <!-- File Input -->
                            <div class="flex-1">
                                <div class="relative">
                                    <input id="profile_image" name="profile_image" type="file" accept="image/*"
                                        class="hidden" onchange="previewImage(this)">
                                    <label for="profile_image"
                                        class="material-button material-button-sm material-button-secondary cursor-pointer flex items-center">
                                        <i class="material-icons mr-2 text-sm">upload</i>
                                        Choose Image
                                    </label>
                                </div>
                                <p class="mt-1 text-xs text-gray-500">PNG, JPG, GIF up to 2MB</p>
                            </div>
                        </div>
                        @error('profile_image')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{{ route('admin.users.index') }}"
                    class="material-button material-button-sm material-button-secondary flex items-center">
                    Cancel
                </a>
                <button type="submit"
                    class="material-button material-button-sm material-button-primary flex items-center">
                    <i class="material-icons mr-2 text-sm">save</i>
                    Create User
                </button>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
    @vite('resources/js/utils/common.js')
    <script src="https://code.jquery.com/jquery-3.7.0.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize password toggles
            initPasswordToggle('toggle-password-admin', 'password', 'password-icon-admin');
            initPasswordToggle('toggle-password-confirm-admin', 'password_confirmation',
                'password-confirm-icon-admin');

            // jQuery Form validation
            $("#user_form").validate({
                rules: {
                    name: {
                        required: true,
                        minlength: 2,
                        maxlength: 255
                    },
                    email: {
                        required: true,
                        email: true,
                        maxlength: 255
                    },
                    password: {
                        required: true,
                        minlength: 8,
                        maxlength: 255
                    },
                    password_confirmation: {
                        required: true,
                        equalTo: "#password"
                    },
                    role: {
                        required: true
                    },
                    status: {
                        required: true
                    },
                    profile_image: {
                        extension: "jpg|jpeg|png|gif",
                        filesize: 2097152 // 2MB in bytes
                    }
                },
                messages: {
                    name: {
                        required: "Please enter the user's full name",
                        minlength: "Name must be at least 2 characters",
                        maxlength: "Name cannot exceed 255 characters"
                    },
                    email: {
                        required: "Please enter an email address",
                        email: "Please enter a valid email address",
                        maxlength: "Email cannot exceed 255 characters"
                    },
                    password: {
                        required: "Please enter a password",
                        minlength: "Password must be at least 8 characters",
                        maxlength: "Password cannot exceed 255 characters"
                    },
                    password_confirmation: {
                        required: "Please confirm the password",
                        equalTo: "Passwords do not match"
                    },
                    role: {
                        required: "Please select a role"
                    },
                    status: {
                        required: "Please select a status"
                    },
                    profile_image: {
                        extension: "Please select a valid image file (JPG, JPEG, PNG, GIF)",
                        filesize: "Image file size must be less than 2MB"
                    }
                },
                errorElement: 'span',
                errorPlacement: function (error, element) {
                    error.addClass("text-red-500 text-xs mt-1").insertAfter(element.closest('.relative'));
                },
                highlight: function (element) {
                    $(element).addClass('border-red-500').removeClass('border-gray-300');
                },
                unhighlight: function (element) {
                    $(element).removeClass('border-red-500').addClass('border-gray-300');
                }
            });

            // Custom validation methods
            $.validator.addMethod("filesize", function(value, element, param) {
                return this.optional(element) || (element.files[0] && element.files[0].size <= param);
            }, "File size must be less than {0} bytes");
        });
    </script>
@endpush



